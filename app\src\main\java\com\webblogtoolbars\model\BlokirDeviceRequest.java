package com.webblogtoolbars.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model untuk request blokir device
 */
public class BlokirDeviceRequest {
    
    @SerializedName("api_key")
    private String apiKey;
    
    @SerializedName("nik")
    private String nik;
    
    @SerializedName("device_id")
    private String deviceId;
    
    @SerializedName("alasan")
    private String alasan;
    
    public BlokirDeviceRequest(String nik, String deviceId, String alasan) {
        this.apiKey = "absensiku_api_key_2023";
        this.nik = nik;
        this.deviceId = deviceId;
        this.alasan = alasan;
    }
    
    public String getApiKey() {
        return apiKey;
    }
    
    public String getNik() {
        return nik;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public String getAlasan() {
        return alasan;
    }
}
