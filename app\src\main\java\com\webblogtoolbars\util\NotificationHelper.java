package com.webblogtoolbars.util;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.AudioAttributes;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.webblogtoolbars.MainActivity;
import com.webblogtoolbars.R;

/**
 * Helper class untuk mengelola notifikasi
 */
public class NotificationHelper {

    private static final String TAG = "NotificationHelper";
    
    // Channel ID untuk notifikasi
    private static final String CHANNEL_ID_CHECKIN = "absensi_checkin_channel";
    private static final String CHANNEL_ID_CHECKOUT = "absensi_checkout_channel";
    
    private final Context context;
    
    public NotificationHelper(Context context) {
        this.context = context;
        createNotificationChannels();
    }
    
    /**
     * Buat channel notifikasi (untuk Android Oreo dan yang lebih baru)
     */
    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = context.getSystemService(NotificationManager.class);
            
            // Channel untuk notifikasi absen masuk
            createNotificationChannel(
                    notificationManager,
                    CHANNEL_ID_CHECKIN,
                    "Pengingat Absen Masuk",
                    "Notifikasi untuk mengingatkan absen masuk",
                    NotificationManager.IMPORTANCE_HIGH,
                    getCheckinSoundUri()
            );
            
            // Channel untuk notifikasi absen pulang
            createNotificationChannel(
                    notificationManager,
                    CHANNEL_ID_CHECKOUT,
                    "Pengingat Absen Pulang",
                    "Notifikasi untuk mengingatkan absen pulang",
                    NotificationManager.IMPORTANCE_HIGH,
                    getCheckoutSoundUri()
            );
            
            Log.d(TAG, "Notification channels created");
        }
    }
    
    /**
     * Buat channel notifikasi
     */
    private void createNotificationChannel(NotificationManager notificationManager, String channelId,
                                          String name, String description, int importance, Uri soundUri) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelId, name, importance);
            channel.setDescription(description);
            channel.enableVibration(true);
            
            // Set sound untuk channel
            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                    .build();
            channel.setSound(soundUri, audioAttributes);
            
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    /**
     * Tampilkan notifikasi pengingat absen masuk
     */
    public void showCheckinNotification(int notificationId) {
        // Intent untuk membuka MainActivity saat notifikasi diklik
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        
        // Buat notifikasi
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID_CHECKIN)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle("Pengingat Absensi")
                .setContentText("Jangan lupa absen masuk hari ini!")
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setSound(getCheckinSoundUri())
                .setVibrate(new long[]{0, 500, 1000})
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
        
        // Tampilkan notifikasi
        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
        try {
            notificationManager.notify(notificationId, builder.build());
            Log.d(TAG, "Notifikasi absen masuk ditampilkan dengan ID: " + notificationId);
        } catch (SecurityException e) {
            Log.e(TAG, "Tidak memiliki izin untuk menampilkan notifikasi: " + e.getMessage());
        }
    }
    
    /**
     * Tampilkan notifikasi pengingat absen pulang
     */
    public void showCheckoutNotification(int notificationId) {
        // Intent untuk membuka MainActivity saat notifikasi diklik
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        
        // Buat notifikasi
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID_CHECKOUT)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle("Pengingat Absensi")
                .setContentText("Jangan lupa absen pulang hari ini!")
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setSound(getCheckoutSoundUri())
                .setVibrate(new long[]{0, 500, 1000})
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
        
        // Tampilkan notifikasi
        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
        try {
            notificationManager.notify(notificationId, builder.build());
            Log.d(TAG, "Notifikasi absen pulang ditampilkan dengan ID: " + notificationId);
        } catch (SecurityException e) {
            Log.e(TAG, "Tidak memiliki izin untuk menampilkan notifikasi: " + e.getMessage());
        }
    }
    
    /**
     * Dapatkan URI sound untuk notifikasi absen masuk
     */
    private Uri getCheckinSoundUri() {
        try {
            return Uri.parse("android.resource://" + context.getPackageName() + "/" + R.raw.absen_masuk);
        } catch (Exception e) {
            Log.e(TAG, "Error menggunakan sound absen_masuk: " + e.getMessage());
            return RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        }
    }
    
    /**
     * Dapatkan URI sound untuk notifikasi absen pulang
     */
    private Uri getCheckoutSoundUri() {
        try {
            return Uri.parse("android.resource://" + context.getPackageName() + "/" + R.raw.absen_pulang);
        } catch (Exception e) {
            Log.e(TAG, "Error menggunakan sound absen_pulang: " + e.getMessage());
            return RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        }
    }
}
