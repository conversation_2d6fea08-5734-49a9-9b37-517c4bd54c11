package com.webblogtoolbars.reminder;

import android.app.AlarmManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build;
import android.util.Log;
import android.app.Notification;

import com.webblogtoolbars.R;
import com.webblogtoolbars.receiver.ReminderReceiver;

import java.util.Calendar;

public class AttendanceReminderManager {
    private static final String TAG = "ReminderManager";
    private static final String CHECKIN_CHANNEL_ID = "checkin_reminder_channel";
    private static final String CHECKOUT_CHANNEL_ID = "checkout_reminder_channel";
    private static final String CHECKIN_CHANNEL_NAME = "Pengingat Absen Masuk";
    private static final String CHECKOUT_CHANNEL_NAME = "Pengingat Absen Pulang";

    // Request code untuk PendingIntent
    private static final int CHECKIN_REQUEST_CODE_1 = 101;
    private static final int CHECKIN_REQUEST_CODE_2 = 102;
    private static final int CHECKOUT_REQUEST_CODE_1 = 103;
    private static final int CHECKOUT_REQUEST_CODE_2 = 104;

    private final Context context;
    private final AlarmManager alarmManager;

    public AttendanceReminderManager(Context context) {
        this.context = context;
        this.alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        createNotificationChannels();
    }

    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = context.getSystemService(NotificationManager.class);

            // Channel untuk absen masuk
            Uri checkinSound = Uri.parse("android.resource://" + context.getPackageName() + "/raw/absen_masuk");
            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                .build();

            NotificationChannel checkinChannel = new NotificationChannel(
                CHECKIN_CHANNEL_ID,
                CHECKIN_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            checkinChannel.setSound(checkinSound, audioAttributes);
            checkinChannel.enableVibration(true);
            checkinChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            checkinChannel.setShowBadge(true);
            // Pastikan notifikasi dapat dihapus
            checkinChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);

            // Channel untuk absen pulang
            Uri checkoutSound = Uri.parse("android.resource://" + context.getPackageName() + "/raw/absen_pulang");
            NotificationChannel checkoutChannel = new NotificationChannel(
                CHECKOUT_CHANNEL_ID,
                CHECKOUT_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            checkoutChannel.setSound(checkoutSound, audioAttributes);
            checkoutChannel.enableVibration(true);
            checkoutChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            checkoutChannel.setShowBadge(true);
            // Pastikan notifikasi dapat dihapus
            checkoutChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);

            // Buat kedua channel
            notificationManager.createNotificationChannel(checkinChannel);
            notificationManager.createNotificationChannel(checkoutChannel);

            Log.d(TAG, "Notification channels created");
        }
    }

    public void scheduleAllReminders() {
        // Jadwalkan semua pengingat
        scheduleCheckinReminders();
        scheduleCheckoutReminders();

        Log.d(TAG, "All reminders scheduled");
    }

    public void scheduleCheckinReminders() {
        // Senin-Kamis: 07:00 dan 07:10
        scheduleWeekdayReminder(7, 0, "checkin", CHECKIN_REQUEST_CODE_1);
        scheduleWeekdayReminder(7, 10, "checkin", CHECKIN_REQUEST_CODE_2);

        // Jumat: 06:00 dan 06:10
        scheduleFridayReminder(6, 0, "checkin", CHECKIN_REQUEST_CODE_1);
        scheduleFridayReminder(6, 10, "checkin", CHECKIN_REQUEST_CODE_2);

        Log.d(TAG, "Check-in reminders scheduled");
    }

    public void scheduleCheckoutReminders() {
        // Senin-Kamis: 15:30 dan 15:40
        scheduleWeekdayReminder(15, 30, "checkout", CHECKOUT_REQUEST_CODE_1);
        scheduleWeekdayReminder(15, 40, "checkout", CHECKOUT_REQUEST_CODE_2);

        // Jumat: 11:30 dan 11:40
        scheduleFridayReminder(11, 30, "checkout", CHECKOUT_REQUEST_CODE_1);
        scheduleFridayReminder(11, 40, "checkout", CHECKOUT_REQUEST_CODE_2);

        Log.d(TAG, "Check-out reminders scheduled");
    }

    private void scheduleWeekdayReminder(int hour, int minute, String type, int requestCode) {
        // Jadwalkan untuk hari Senin-Kamis
        for (int dayOfWeek = Calendar.MONDAY; dayOfWeek <= Calendar.THURSDAY; dayOfWeek++) {
            scheduleReminderForDay(dayOfWeek, hour, minute, type, requestCode);
        }
    }

    private void scheduleFridayReminder(int hour, int minute, String type, int requestCode) {
        // Jadwalkan untuk hari Jumat
        scheduleReminderForDay(Calendar.FRIDAY, hour, minute, type, requestCode);
    }

    private void scheduleReminderForDay(int dayOfWeek, int hour, int minute, String type, int requestCode) {
        Calendar calendar = Calendar.getInstance();

        // Set hari dalam minggu
        int currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int daysToAdd = (dayOfWeek - currentDayOfWeek + 7) % 7;

        if (daysToAdd == 0) {
            // Hari ini adalah hari yang diinginkan, periksa waktu
            Calendar targetTime = (Calendar) calendar.clone();
            targetTime.set(Calendar.HOUR_OF_DAY, hour);
            targetTime.set(Calendar.MINUTE, minute);
            targetTime.set(Calendar.SECOND, 0);
            targetTime.set(Calendar.MILLISECOND, 0);

            if (targetTime.getTimeInMillis() <= System.currentTimeMillis()) {
                // Waktu sudah lewat, jadwalkan untuk minggu depan
                daysToAdd = 7;
            }
        }

        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // Buat intent
        Intent intent = new Intent(context, ReminderReceiver.class);
        intent.putExtra("type", type);

        if ("checkin".equals(type)) {
            intent.putExtra("message", "Jangan lupa absen masuk hari ini!");
            intent.putExtra("channelId", CHECKIN_CHANNEL_ID);
        } else {
            intent.putExtra("message", "Jangan lupa absen pulang hari ini!");
            intent.putExtra("channelId", CHECKOUT_CHANNEL_ID);
        }

        // Buat PendingIntent yang unik untuk setiap hari
        int uniqueRequestCode = requestCode + (dayOfWeek * 100);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            uniqueRequestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // Jadwalkan alarm
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                calendar.getTimeInMillis(),
                pendingIntent
            );
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            alarmManager.setExact(
                AlarmManager.RTC_WAKEUP,
                calendar.getTimeInMillis(),
                pendingIntent
            );
        } else {
            alarmManager.set(
                AlarmManager.RTC_WAKEUP,
                calendar.getTimeInMillis(),
                pendingIntent
            );
        }

        Log.d(TAG, "Reminder scheduled for " + calendar.getTime() +
            " (Day: " + dayOfWeek + ", Type: " + type + ")");
    }

    public void cancelAllReminders() {
        // Batalkan semua pengingat
        for (int dayOfWeek = Calendar.SUNDAY; dayOfWeek <= Calendar.SATURDAY; dayOfWeek++) {
            // Batalkan pengingat absen masuk
            cancelReminder(CHECKIN_REQUEST_CODE_1 + (dayOfWeek * 100));
            cancelReminder(CHECKIN_REQUEST_CODE_2 + (dayOfWeek * 100));

            // Batalkan pengingat absen pulang
            cancelReminder(CHECKOUT_REQUEST_CODE_1 + (dayOfWeek * 100));
            cancelReminder(CHECKOUT_REQUEST_CODE_2 + (dayOfWeek * 100));
        }

        Log.d(TAG, "All reminders canceled");
    }

    private void cancelReminder(int requestCode) {
        Intent intent = new Intent(context, ReminderReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        );

        alarmManager.cancel(pendingIntent);
    }

    public void testCheckinReminder() {
        Intent intent = new Intent(context, ReminderReceiver.class);
        intent.putExtra("type", "checkin");
        intent.putExtra("message", "Jangan lupa absen masuk hari ini!");
        intent.putExtra("channelId", CHECKIN_CHANNEL_ID);
        context.sendBroadcast(intent);

        Log.d(TAG, "Test check-in reminder sent");
    }

    public void testCheckoutReminder() {
        Intent intent = new Intent(context, ReminderReceiver.class);
        intent.putExtra("type", "checkout");
        intent.putExtra("message", "Jangan lupa absen pulang hari ini!");
        intent.putExtra("channelId", CHECKOUT_CHANNEL_ID);
        context.sendBroadcast(intent);

        Log.d(TAG, "Test check-out reminder sent");
    }
}
