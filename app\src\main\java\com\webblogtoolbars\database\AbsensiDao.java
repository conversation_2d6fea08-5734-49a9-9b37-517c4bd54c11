package com.webblogtoolbars.database;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.webblogtoolbars.model.AbsensiData;

import java.util.List;

/**
 * Data Access Object untuk AbsensiData
 */
@Dao
public interface AbsensiDao {

    @Insert
    long insert(AbsensiData absensiData);

    @Update
    void update(AbsensiData absensiData);

    @Query("UPDATE absensi_data SET isSynced = 1 WHERE id = :id")
    void markAsSynced(int id);

    @Query("SELECT * FROM absensi_data WHERE isSynced = 0")
    List<AbsensiData> getUnsyncedAbsensi();

    @Query("SELECT * FROM absensi_data WHERE id = :id")
    AbsensiData getAbsensiById(int id);

    @Query("DELETE FROM absensi_data WHERE isSynced = 1 AND tanggal < date('now', '-7 day')")
    void deleteOldSyncedData();

    @Query("DELETE FROM absensi_data WHERE isSynced = 1")
    void deleteAllSyncedData();

    @Query("DELETE FROM absensi_data")
    void deleteAllData();

    @Query("SELECT * FROM absensi_data ORDER BY id DESC")
    LiveData<List<AbsensiData>> getAllAbsensi();

    @Query("DELETE FROM absensi_data WHERE id = :id")
    void deleteById(int id);
}
