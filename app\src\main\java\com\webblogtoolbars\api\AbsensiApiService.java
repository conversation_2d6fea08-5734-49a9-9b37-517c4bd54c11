package com.webblogtoolbars.api;

import com.webblogtoolbars.model.AbsensiRequest;
import com.webblogtoolbars.model.ApiResponse;
import com.webblogtoolbars.model.BlokirDeviceRequest;
import com.webblogtoolbars.model.CekDeviceRequest;
import com.webblogtoolbars.model.DeviceStatusRequest;
import com.webblogtoolbars.model.DeviceStatusResponse;
import com.webblogtoolbars.model.LocationTrackingRequest;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Interface untuk API service absensi
 */
public interface AbsensiApiService {

    @POST("api/absensi_offline.php")
    Call<ApiResponse> submitAbsensi(@Body AbsensiRequest request);

    @POST("api/blokir_device.php")
    Call<ApiResponse> blokirDevice(@Body BlokirDeviceRequest request);

    // Metode lama dengan parameter query
    @GET("api/cek_device.php")
    Call<ApiResponse> cekDevice(@Query("nik") String nik, @Query("device_id") String deviceId);

    // Metode baru dengan body request
    @POST("api/cek_device.php")
    Call<ApiResponse> cekDevicePost(@Body CekDeviceRequest request);

    // API baru untuk cek status device
    @POST("api/get_device_status.php")
    Call<DeviceStatusResponse> getDeviceStatus(@Body DeviceStatusRequest request);

    @POST("api/track_location.php")
    Call<ApiResponse> trackLocation(@Body LocationTrackingRequest request);
}
