package com.webblogtoolbars;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.work.Constraints;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import android.Manifest;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.webkit.GeolocationPermissions;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.net.HttpURLConnection;
import java.net.URL;
import android.os.AsyncTask;
import android.app.AlarmManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.location.LocationManager;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.webblogtoolbars.api.AbsensiApiService;
import com.webblogtoolbars.api.ApiClient;
import com.webblogtoolbars.database.AbsensiDatabase;
import com.webblogtoolbars.model.AbsensiData;
import com.webblogtoolbars.model.ApiResponse;
import com.webblogtoolbars.model.BlokirDeviceRequest;
import com.webblogtoolbars.model.CekDeviceRequest;
import com.webblogtoolbars.model.DeviceStatusRequest;
import com.webblogtoolbars.model.DeviceStatusResponse;
import com.webblogtoolbars.service.LocationTrackingService;
import com.webblogtoolbars.reminder.AttendanceReminderManager;
import com.webblogtoolbars.util.DeviceUtils;
import com.webblogtoolbars.util.MockLocationDetector;
import com.webblogtoolbars.util.NetworkUtil;
import com.webblogtoolbars.worker.SyncWorker;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class MainActivity extends AppCompatActivity {

    private WebView webView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RelativeLayout progressBarLayout; // Layout untuk progress bar floating
    private static final int ALL_PERMISSIONS_REQUEST = 1;
    private static final int FILE_CHOOSER_RESULT_CODE = 2;
    private String currentUrl;
    private ValueCallback<Uri[]> filePathCallback;
    private Uri cameraImageUri;
    private Button btnTestTracking; // Tombol untuk testing tracking lokasi

    private static final String TAG = "MainActivity";
    private static final String PREF_NAME = "AbsensikuPrefs";
    private static final String KEY_REMEMBERED_NIK = "rememberedNik";
    private static final String KEY_DEVICE_ID = "deviceId";
    private static final String KEY_DEVICE_BLOCKED = "deviceBlocked";
    private static final String KEY_FIRST_TIME = "firstTime";
    private static final String KEY_AGREEMENT_ACCEPTED = "agreementAccepted";

    private String deviceId;
    private String rememberedNik;
    private Intent locationTrackingServiceIntent;
    private AttendanceReminderManager reminderManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Periksa apakah persetujuan telah diterima
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        boolean isAgreementAccepted = prefs.getBoolean(KEY_AGREEMENT_ACCEPTED, false);

        if (!isAgreementAccepted) {
            // Jika persetujuan belum diterima, arahkan ke WelcomeActivity
            Intent intent = new Intent(this, WelcomeActivity.class);
            startActivity(intent);
            finish();
            return;
        }

        // Periksa apakah activity dibuka dari notifikasi yang dihapus
        if (getIntent() != null && "DISMISS_NOTIFICATION".equals(getIntent().getAction())) {
            // Tidak perlu melakukan apa-apa, hanya untuk menghapus notifikasi
            finish();
            return;
        }

        setContentView(R.layout.activity_main);

        // Inisialisasi device ID
        initDeviceId();

        // Periksa fake GPS
        checkMockLocation();

        // Periksa apakah device diblokir
        checkDeviceBlocked();

        // Periksa dan minta izin
        checkAndRequestPermissions();

        webView = findViewById(R.id.webView);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        progressBarLayout = findViewById(R.id.progressBarLayout);

        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setGeolocationEnabled(true);
        webSettings.setMediaPlaybackRequiresUserGesture(false);

        // Tambahkan JavaScript interface untuk mendapatkan NIK
        webView.addJavascriptInterface(new WebAppInterface(this), "Android");

        setupWebView();
        setupSwipeRefresh();
        setupPeriodicSync();

        currentUrl = "https://absensiku.trunois.my.id/karyawan/index.php";

        // Selalu coba load URL terlebih dahulu
        checkInternetConnection(currentUrl);

        // Inisialisasi intent untuk service tracking lokasi
        locationTrackingServiceIntent = new Intent(this, LocationTrackingService.class);

        // Inisialisasi reminder manager
        reminderManager = new AttendanceReminderManager(this);

        // Tambahkan tombol test untuk tracking lokasi (hanya untuk testing)
        addTestTrackingButton();

        // Mulai service tracking lokasi
        startLocationTrackingService();

        // Jadwalkan pengingat absensi
        reminderManager.scheduleAllReminders();
    }

    /**
     * Tambahkan tombol test untuk tracking lokasi dan notifikasi (hanya untuk testing)
     */
    private void addTestTrackingButton() {
        // Layout untuk tombol-tombol
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.VERTICAL);
        buttonLayout.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));

        // Tombol test tracking
        btnTestTracking = new Button(this);
        btnTestTracking.setText("Test Tracking");
        btnTestTracking.setOnClickListener(v -> {
            // Mulai service tracking lokasi
            startLocationTrackingService();
            Toast.makeText(this, "Service tracking lokasi dimulai", Toast.LENGTH_SHORT).show();
        });

        // Tombol test notifikasi masuk
        Button btnTestNotifMasuk = new Button(this);
        btnTestNotifMasuk.setText("Test Notif Masuk");
        btnTestNotifMasuk.setOnClickListener(v -> {
            // Test pengingat absen masuk
            reminderManager.testCheckinReminder();
            Toast.makeText(this, "Menampilkan notifikasi absen masuk", Toast.LENGTH_SHORT).show();
        });

        // Tombol test notifikasi pulang
        Button btnTestNotifPulang = new Button(this);
        btnTestNotifPulang.setText("Test Notif Pulang");
        btnTestNotifPulang.setOnClickListener(v -> {
            // Test pengingat absen pulang
            reminderManager.testCheckoutReminder();
            Toast.makeText(this, "Menampilkan notifikasi absen pulang", Toast.LENGTH_SHORT).show();
        });

        // Tombol jadwalkan notifikasi
        Button btnScheduleNotif = new Button(this);
        btnScheduleNotif.setText("Jadwalkan Notifikasi");
        btnScheduleNotif.setOnClickListener(v -> {
            // Jadwalkan semua pengingat
            reminderManager.scheduleAllReminders();
            Toast.makeText(this, "Notifikasi dijadwalkan", Toast.LENGTH_SHORT).show();
        });

        // Tambahkan tombol-tombol ke layout
        buttonLayout.addView(btnTestTracking);
        buttonLayout.addView(btnTestNotifMasuk);
        buttonLayout.addView(btnTestNotifPulang);
        buttonLayout.addView(btnScheduleNotif);

        // Tambahkan layout ke swipeRefreshLayout
        swipeRefreshLayout.addView(buttonLayout);
    }

    /**
     * Mulai service tracking lokasi
     */
    private void startLocationTrackingService() {
        // Cek apakah NIK tersedia
        if (rememberedNik == null || rememberedNik.isEmpty()) {
            Log.d(TAG, "NIK tidak tersedia, tidak memulai service tracking lokasi");
            return;
        }

        // Mulai service
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(locationTrackingServiceIntent);
        } else {
            startService(locationTrackingServiceIntent);
        }

        Log.d(TAG, "Service tracking lokasi dimulai");
    }



    private Handler periodicCheckHandler;
    private static final int CHECK_INTERVAL = 60000; // 1 menit

    private void setupPeriodicSync() {
        // Periksa apakah ada data yang belum disinkronisasi
        checkUnsyncedData();

        // Setup periodic check
        periodicCheckHandler = new Handler(Looper.getMainLooper());
        periodicCheckHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // Periksa data yang belum disinkronisasi
                checkUnsyncedData();

                // Periksa status blokir device jika NIK dan deviceId tersedia
                SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
                String nik = prefs.getString(KEY_REMEMBERED_NIK, "");
                boolean isBlocked = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);

                // Jika device diblokir, periksa status di server secara periodik
                if (isBlocked && deviceId != null && !nik.isEmpty()) {
                    Log.d(TAG, "Memeriksa status blokir device secara periodik");
                    checkDeviceBlockedOnServer(nik, deviceId);
                }

                // Jadwalkan pemeriksaan berikutnya
                periodicCheckHandler.postDelayed(this, CHECK_INTERVAL);
            }
        }, CHECK_INTERVAL);
    }

    // Flag untuk melacak apakah dialog sinkronisasi sudah ditampilkan
    private boolean syncDialogShown = false;

    private void checkUnsyncedData() {
        // Jika dialog sudah ditampilkan, jangan tampilkan lagi
        if (syncDialogShown) {
            return;
        }

        // Buat instance database
        AbsensiDatabase database = AbsensiDatabase.getDatabase(this);

        // Buat executor service untuk operasi database
        ExecutorService executorService = Executors.newSingleThreadExecutor();

        // Periksa data yang belum disinkronisasi
        executorService.execute(() -> {
            try {
                // Ambil data yang belum disinkronisasi
                List<AbsensiData> unsyncedData = database.absensiDao().getUnsyncedAbsensi();

                // Jika ada data yang belum disinkronisasi, tampilkan dialog
                if (!unsyncedData.isEmpty()) {
                    runOnUiThread(() -> {
                        // Set flag bahwa dialog sudah ditampilkan
                        syncDialogShown = true;
                        showSyncDialog(unsyncedData.size());
                    });
                }
            } catch (Exception e) {
                Log.e("MainActivity", "Error saat memeriksa data yang belum disinkronisasi: " + e.getMessage());
            }
        });
    }

    private void showSyncDialog(int dataCount) {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Sinkronisasi Data");
        builder.setMessage("Terdapat " + dataCount + " data absensi yang belum disinkronisasi. Apakah Anda ingin mengirim data ke server sekarang?");
        builder.setPositiveButton("Ya, Sinkronkan", (dialog, which) -> {
            syncData();
        });
        builder.setNegativeButton("Nanti Saja", (dialog, which) -> {
            dialog.dismiss();
            // Reset flag sinkronisasi dialog agar bisa muncul lagi nanti
            syncDialogShown = false;
        });
        builder.setCancelable(false);
        builder.show();
    }

    private void syncData() {
        // Buat constraints untuk worker
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();

        // Buat work request untuk sinkronisasi
        OneTimeWorkRequest syncWorkRequest = new OneTimeWorkRequest.Builder(SyncWorker.class)
                .setConstraints(constraints)
                .build();

        // Jadwalkan worker
        WorkManager.getInstance(this).enqueue(syncWorkRequest);

        // Tampilkan toast bahwa sinkronisasi sedang berjalan
        Toast.makeText(this, "Sinkronisasi data sedang berjalan di latar belakang", Toast.LENGTH_SHORT).show();

        // Tambahkan listener untuk mengetahui status sinkronisasi
        WorkManager.getInstance(this).getWorkInfoByIdLiveData(syncWorkRequest.getId())
                .observe(this, workInfo -> {
                    if (workInfo != null) {
                        if (workInfo.getState() == androidx.work.WorkInfo.State.SUCCEEDED) {
                            // Hapus semua data dari database
                            deleteAllData();

                            // Tampilkan dialog sukses
                            showSuccessDialog();
                        } else if (workInfo.getState() == androidx.work.WorkInfo.State.FAILED) {
                            // Tampilkan pesan error
                            Toast.makeText(this, "Sinkronisasi gagal", Toast.LENGTH_SHORT).show();
                        }
                    }
                });
    }

    private void deleteAllData() {
        // Buat instance database
        AbsensiDatabase database = AbsensiDatabase.getDatabase(this);

        // Buat executor service untuk operasi database
        ExecutorService executorService = Executors.newSingleThreadExecutor();

        // Hapus semua data
        executorService.execute(() -> {
            try {
                // Hapus semua data dari database
                database.absensiDao().deleteAllData();
                Log.d("MainActivity", "Semua data berhasil dihapus dari database lokal");

                // Hapus data dari cache
                database.clearAllTables();
            } catch (Exception e) {
                Log.e("MainActivity", "Error saat menghapus data: " + e.getMessage());
            }
        });
    }

    private void showSuccessDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Sinkronisasi Berhasil");
        builder.setMessage("Data absensi telah berhasil dikirim ke server dan dihapus dari penyimpanan lokal.");
        builder.setPositiveButton("OK", (dialog, which) -> {
            dialog.dismiss();
            // Reset flag sinkronisasi dialog
            syncDialogShown = false;
        });
        builder.setCancelable(false);
        builder.show();
    }

    private void setupSwipeRefresh() {
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                refreshWebView();
            }
        });

        // Mengatur warna indikator refresh
        swipeRefreshLayout.setColorSchemeResources(
            android.R.color.holo_blue_bright,
            android.R.color.holo_green_light,
            android.R.color.holo_orange_light,
            android.R.color.holo_red_light
        );
    }

    private void refreshWebView() {
        if (currentUrl != null && !currentUrl.isEmpty()) {
            checkInternetConnection(currentUrl);
        } else {
            swipeRefreshLayout.setRefreshing(false);
        }
    }

    private void setupWebView() {
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
                callback.invoke(origin, true, false);
            }

            @Override
            public void onPermissionRequest(final PermissionRequest request) {
                MainActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (request.getResources().length > 0) {
                            request.grant(request.getResources());
                        } else {
                            request.deny();
                        }
                    }
                });
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                // Update progress bar saat loading
                if (newProgress < 100) {
                    progressBarLayout.setVisibility(View.VISIBLE);
                } else {
                    progressBarLayout.setVisibility(View.GONE);
                }
            }

            // Menangani pemilihan file
            @Override
            public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
                // Simpan callback
                if (MainActivity.this.filePathCallback != null) {
                    MainActivity.this.filePathCallback.onReceiveValue(null);
                }
                MainActivity.this.filePathCallback = filePathCallback;

                Intent intent = fileChooserParams.createIntent();
                intent.addCategory(Intent.CATEGORY_OPENABLE);

                // Tambahkan intent untuk mengambil gambar dari kamera jika diperlukan
                Intent[] intentArray;
                intentArray = new Intent[]{intent};

                Intent chooserIntent = new Intent(Intent.ACTION_CHOOSER);
                chooserIntent.putExtra(Intent.EXTRA_INTENT, intent);
                chooserIntent.putExtra(Intent.EXTRA_TITLE, "Pilih File");
                chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, intentArray);

                try {
                    startActivityForResult(chooserIntent, FILE_CHOOSER_RESULT_CODE);
                } catch (Exception e) {
                    filePathCallback.onReceiveValue(null);
                    MainActivity.this.filePathCallback = null;
                    return false;
                }
                return true;
            }
        });

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                String url = request.getUrl().toString();
                currentUrl = url;

                // Tampilkan progress bar saat mulai loading
                progressBarLayout.setVisibility(View.VISIBLE);

                // Jika URL berisi domain absensiku.trunois.my.id, maka kita izinkan
                if (url.contains("absensiku.trunois.my.id")) {
                    // Biarkan WebView memuat URL secara langsung tanpa pemeriksaan koneksi tambahan
                    return false;
                } else {
                    // Untuk domain lain, periksa koneksi terlebih dahulu
                    checkInternetConnection(url);
                    return true;
                }
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                // Tampilkan progress bar saat halaman mulai dimuat
                progressBarLayout.setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // Menyembunyikan indikator refresh ketika halaman selesai dimuat
                swipeRefreshLayout.setRefreshing(false);

                // Sembunyikan progress bar saat halaman selesai dimuat
                progressBarLayout.setVisibility(View.GONE);

                // Inject JavaScript untuk menangkap NIK saat login
                if (url.contains("login.php") || url.contains("index.php")) {
                    injectNikCaptureScript(view);
                }

                // Inject JavaScript untuk reset status blokir di halaman admin
                if (url.contains("admin") || url.contains("blokir_device.php")) {
                    injectResetBlockScript(view);
                }
            }

            /**
             * Inject JavaScript untuk menangkap NIK saat login
             */
            private void injectNikCaptureScript(WebView view) {
                String script = "javascript:(function() {" +
                        "var loginForm = document.querySelector('form');" +
                        "if (loginForm) {" +
                        "   loginForm.addEventListener('submit', function() {" +
                        "       var nikInput = document.querySelector('input[name=\"nik\"]');" +
                        "       if (nikInput && nikInput.value) {" +
                        "           Android.saveNik(nikInput.value);" +
                        "       }" +
                        "   });" +
                        "}" +
                        "})()";

                view.evaluateJavascript(script, null);
            }

            /**
             * Inject JavaScript untuk reset status blokir di halaman admin
             */
            private void injectResetBlockScript(WebView view) {
                String script = "javascript:(function() {" +
                        "// Cek apakah halaman memiliki tabel data device yang diblokir" +
                        "var table = document.querySelector('table');" +
                        "if (table) {" +
                        "   // Tambahkan tombol reset di setiap baris tabel" +
                        "   var rows = table.querySelectorAll('tr');" +
                        "   for (var i = 1; i < rows.length; i++) {" + // Mulai dari 1 untuk melewati header
                        "       var row = rows[i];" +
                        "       var lastCell = row.querySelector('td:last-child');" +
                        "       if (lastCell) {" +
                        "           // Tambahkan tombol reset jika belum ada" +
                        "           if (!lastCell.querySelector('.reset-button')) {" +
                        "               var resetButton = document.createElement('button');" +
                        "               resetButton.innerText = 'Reset Status';" +
                        "               resetButton.className = 'reset-button';" +
                        "               resetButton.style.marginLeft = '5px';" +
                        "               resetButton.addEventListener('click', function() {" +
                        "                   Android.resetDeviceBlockStatus();" +
                        "                   alert('Status blokir device telah direset');" +
                        "               });" +
                        "               lastCell.appendChild(resetButton);" +
                        "           }" +
                        "       }" +
                        "   }" +
                        "}" +
                        "})()";

                view.evaluateJavascript(script, null);
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                // Menyembunyikan indikator refresh ketika terjadi error
                swipeRefreshLayout.setRefreshing(false);

                // Tampilkan dialog konfirmasi untuk beralih ke mode offline
                android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(MainActivity.this);
                builder.setTitle("Kesalahan Koneksi");
                builder.setMessage("Terjadi kesalahan saat memuat halaman. Apakah Anda ingin beralih ke mode offline?");
                builder.setPositiveButton("Ya", (dialog, which) -> {
                    startActivity(new Intent(MainActivity.this, OfflineAbsensiActivity.class));
                });
                builder.setNegativeButton("Tidak", (dialog, which) -> {
                    dialog.dismiss();
                });
                builder.setCancelable(false);
                builder.show();
            }
        });
    }



    @Override
    protected void onResume() {
        super.onResume();

        // Periksa status GPS
        checkGpsStatus();

        // Periksa fake GPS setiap kali aplikasi di-resume
        checkMockLocation();

        // Periksa apakah device diblokir
        checkDeviceBlocked();

        // Mulai service tracking lokasi jika NIK tersedia
        if (rememberedNik != null && !rememberedNik.isEmpty()) {
            startLocationTrackingService();
        }
    }

    /**
     * Memeriksa status GPS dan menampilkan dialog peringatan jika GPS tidak aktif
     */
    private void checkGpsStatus() {
        LocationManager locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        boolean isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);

        if (!isGpsEnabled) {
            showGpsDisabledAlert();
        }
    }

    /**
     * Menampilkan dialog peringatan jika GPS tidak aktif
     */
    private void showGpsDisabledAlert() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("GPS Tidak Aktif");
        builder.setMessage("Aplikasi ini memerlukan GPS untuk berfungsi dengan baik. Silakan aktifkan GPS Anda.");
        builder.setCancelable(false);

        builder.setPositiveButton("Aktifkan GPS", (dialog, which) -> {
            // Buka pengaturan lokasi
            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            startActivity(intent);
        });

        builder.setNegativeButton("Keluar", (dialog, which) -> {
            dialog.dismiss();
            // Tutup aplikasi jika pengguna menolak mengaktifkan GPS
            finish();
        });

        builder.show();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    private void checkAndRequestPermissions() {
        String[] permissions;

        // Untuk Android 13 (API level 33) dan yang lebih baru, tambahkan izin POST_NOTIFICATIONS
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions = new String[]{
                Manifest.permission.CAMERA,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.FOREGROUND_SERVICE,
                Manifest.permission.RECEIVE_BOOT_COMPLETED,
                Manifest.permission.POST_NOTIFICATIONS
            };
        } else {
            permissions = new String[]{
                Manifest.permission.CAMERA,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.FOREGROUND_SERVICE,
                Manifest.permission.RECEIVE_BOOT_COMPLETED
            };
        }

        // Untuk Android 12 (API level 31) dan yang lebih baru, periksa izin SCHEDULE_EXACT_ALARM
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestScheduleExactAlarmPermission();
        }

        boolean shouldRequestPermissions = false;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                shouldRequestPermissions = true;
                break;
            }
        }

        if (shouldRequestPermissions) {
            ActivityCompat.requestPermissions(this, permissions, ALL_PERMISSIONS_REQUEST);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == ALL_PERMISSIONS_REQUEST) {
            boolean allPermissionsGranted = true;
            boolean notificationPermissionDenied = false;
            boolean cameraPermissionDenied = false;

            for (int i = 0; i < permissions.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;

                    // Cek apakah izin notifikasi yang ditolak
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
                            permissions[i].equals(Manifest.permission.POST_NOTIFICATIONS)) {
                        notificationPermissionDenied = true;
                    }

                    // Cek apakah izin kamera yang ditolak
                    if (permissions[i].equals(Manifest.permission.CAMERA)) {
                        cameraPermissionDenied = true;
                    }
                }
            }

            if (allPermissionsGranted) {
                // Semua izin diberikan
                Log.d(TAG, "Semua izin diberikan");

                // Jadwalkan pengingat absensi
                if (reminderManager != null) {
                    reminderManager.scheduleAllReminders();
                }
            } else {
                // Beberapa izin ditolak
                Log.d(TAG, "Beberapa izin ditolak");

                // Jika izin kamera ditolak, tampilkan dialog peringatan
                if (cameraPermissionDenied) {
                    showCameraPermissionDialog();
                }
                // Jika izin notifikasi ditolak, tampilkan dialog
                else if (notificationPermissionDenied) {
                    showNotificationPermissionDialog();
                }
            }
        }
    }

    /**
     * Tampilkan dialog peringatan izin kamera
     */
    private void showCameraPermissionDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Izin Kamera Diperlukan");
        builder.setMessage("Aplikasi ini memerlukan izin kamera untuk berfungsi dengan baik. Tanpa izin ini, aplikasi tidak dapat digunakan.");
        builder.setCancelable(false);

        builder.setPositiveButton("Buka Pengaturan", (dialog, which) -> {
            // Buka pengaturan aplikasi
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", getPackageName(), null);
            intent.setData(uri);
            startActivity(intent);
            dialog.dismiss();
        });

        builder.setNegativeButton("Keluar", (dialog, which) -> {
            dialog.dismiss();
            // Tutup aplikasi jika pengguna menolak mengaktifkan kamera
            finish();
        });

        builder.show();
    }

    /**
     * Tampilkan dialog izin notifikasi
     */
    private void showNotificationPermissionDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Izin Notifikasi Diperlukan");
        builder.setMessage("Aplikasi ini memerlukan izin notifikasi untuk mengingatkan Anda tentang absensi. " +
                "Tanpa izin ini, Anda tidak akan menerima pengingat absensi.");
        builder.setPositiveButton("Buka Pengaturan", (dialog, which) -> {
            // Buka pengaturan aplikasi
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", getPackageName(), null);
            intent.setData(uri);
            startActivity(intent);
            dialog.dismiss();
        });
        builder.setNegativeButton("Nanti", (dialog, which) -> {
            dialog.dismiss();
        });
        builder.setCancelable(false);
        builder.show();
    }

    /**
     * Minta izin SCHEDULE_EXACT_ALARM untuk Android 12 (API level 31) dan yang lebih baru
     */
    private void requestScheduleExactAlarmPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            AlarmManager alarmManager = (AlarmManager) getSystemService(ALARM_SERVICE);
            if (!alarmManager.canScheduleExactAlarms()) {
                android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
                builder.setTitle("Izin Alarm Diperlukan");
                builder.setMessage("Aplikasi ini memerlukan izin untuk menjadwalkan alarm tepat waktu " +
                        "agar pengingat absensi berfungsi dengan baik.");
                builder.setPositiveButton("Buka Pengaturan", (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
                    intent.setData(Uri.fromParts("package", getPackageName(), null));
                    startActivity(intent);
                    dialog.dismiss();
                });
                builder.setNegativeButton("Nanti", (dialog, which) -> {
                    dialog.dismiss();
                });
                builder.setCancelable(false);
                builder.show();
            }
        }
    }

    private void checkInternetConnection(String url) {
        // Jika URL sudah dimuat dan berisi domain yang sama, tidak perlu memeriksa koneksi lagi
        String currentWebViewUrl = webView.getUrl();
        if (currentWebViewUrl != null && currentWebViewUrl.contains("absensiku.trunois.my.id")
                && url.contains("absensiku.trunois.my.id") && !url.equals("https://absensiku.trunois.my.id")) {
            // Langsung muat URL tanpa pemeriksaan koneksi
            webView.loadUrl(url);
            return;
        }

        // Menampilkan indikator refresh jika belum ditampilkan
        if (!swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(true);
        }
        new InternetCheckTask(url).execute();
    }

    private class InternetCheckTask extends AsyncTask<Void, Void, Boolean> {
        private String url;

        public InternetCheckTask(String url) {
            this.url = url;
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                // Jika URL berisi domain absensiku.trunois.my.id, periksa koneksi ke domain utama saja
                String checkUrl = url;
                if (url.contains("absensiku.trunois.my.id")) {
                    // Periksa koneksi ke domain utama
                    checkUrl = "https://absensiku.trunois.my.id";
                }

                HttpURLConnection connection = (HttpURLConnection) new URL(checkUrl).openConnection();
                connection.setConnectTimeout(5000);
                connection.setRequestMethod("HEAD"); // Gunakan HEAD untuk pemeriksaan yang lebih cepat
                connection.connect();

                int responseCode = connection.getResponseCode();
                return responseCode >= 200 && responseCode < 400; // Kode 2xx dan 3xx dianggap sukses
            } catch (Exception e) {
                Log.e("MainActivity", "Error checking connection: " + e.getMessage());
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean isConnected) {
            // Menyembunyikan indikator refresh
            swipeRefreshLayout.setRefreshing(false);

            if (isConnected) {
                // Jika URL berisi domain absensiku.trunois.my.id dan bukan halaman utama,
                // biarkan WebView memuat URL secara langsung
                if (url.contains("absensiku.trunois.my.id") && !url.equals("https://absensiku.trunois.my.id")) {
                    // Jangan muat ulang jika URL sudah dimuat
                    if (!url.equals(webView.getUrl())) {
                        webView.loadUrl(url);
                    }
                } else {
                    webView.loadUrl(url);
                }
            } else {
                // Periksa apakah WebView sudah memuat halaman dari domain yang sama
                String currentWebViewUrl = webView.getUrl();
                if (currentWebViewUrl != null && currentWebViewUrl.contains("absensiku.trunois.my.id")) {
                    // Jika sudah memuat halaman dari domain yang sama, biarkan saja
                    Log.d("MainActivity", "Tetap di halaman saat ini: " + currentWebViewUrl);
                } else {
                    // Tampilkan dialog konfirmasi untuk beralih ke mode offline
                    android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(MainActivity.this);
                    builder.setTitle("Tidak Dapat Terhubung");
                    builder.setMessage("Tidak dapat terhubung ke server. Apakah Anda ingin beralih ke mode offline?");
                    builder.setPositiveButton("Ya", (dialog, which) -> {
                        startActivity(new Intent(MainActivity.this, OfflineAbsensiActivity.class));
                    });
                    builder.setNegativeButton("Coba Lagi", (dialog, which) -> {
                        dialog.dismiss();
                        checkInternetConnection(url);
                    });
                    builder.setCancelable(false);
                    builder.show();
                }
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == FILE_CHOOSER_RESULT_CODE) {
            if (filePathCallback == null) {
                return;
            }

            Uri[] results = null;

            // Periksa hasil
            if (resultCode == RESULT_OK) {
                if (data != null) {
                    String dataString = data.getDataString();
                    if (dataString != null) {
                        results = new Uri[]{Uri.parse(dataString)};
                    }
                }
            }

            filePathCallback.onReceiveValue(results);
            filePathCallback = null;
        }
    }

    /**
     * Inisialisasi device ID
     */
    private void initDeviceId() {
        // Dapatkan device ID dari SharedPreferences atau generate baru
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        deviceId = prefs.getString(KEY_DEVICE_ID, null);

        if (deviceId == null || deviceId.isEmpty()) {
            // Generate device ID baru
            deviceId = DeviceUtils.getDeviceId(this);

            // Simpan device ID
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_DEVICE_ID, deviceId);
            editor.apply();
        }

        Log.d(TAG, "Device ID: " + deviceId);
    }

    /**
     * Periksa apakah fake GPS diaktifkan
     */
    private void checkMockLocation() {
        if (MockLocationDetector.isMockLocationEnabled(this) || MockLocationDetector.areMockLocationAppsInstalled(this)) {
            // Fake GPS terdeteksi
            Log.d(TAG, "Fake GPS terdeteksi");

            // Dapatkan NIK dari SharedPreferences
            SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
            rememberedNik = prefs.getString(KEY_REMEMBERED_NIK, "");

            // Jika NIK tersedia, kirim ke server
            if (!rememberedNik.isEmpty() && deviceId != null) {
                // Tampilkan peringatan
                showMockLocationWarning();

                // Kirim data ke server
                reportMockLocation(rememberedNik, deviceId);
            }
        }
    }

    /**
     * Tampilkan peringatan fake GPS
     */
    private void showMockLocationWarning() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Peringatan Keamanan");
        builder.setMessage("Aplikasi mendeteksi penggunaan Fake GPS atau Mock Location. " +
                "Penggunaan aplikasi ini tidak diizinkan dan perangkat Anda akan diblokir.");
        builder.setPositiveButton("OK", (dialog, which) -> {
            dialog.dismiss();
            finish();
        });
        builder.setCancelable(false);
        builder.show();
    }

    /**
     * Kirim laporan fake GPS ke server
     */
    private void reportMockLocation(String nik, String deviceId) {
        AbsensiApiService apiService = ApiClient.getClient().create(AbsensiApiService.class);
        BlokirDeviceRequest request = new BlokirDeviceRequest(nik, deviceId, "Penggunaan Fake GPS terdeteksi");

        Call<ApiResponse> call = apiService.blokirDevice(request);
        call.enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    Log.d(TAG, "Berhasil melaporkan penggunaan Fake GPS: " + response.body().getMessage());

                    // Tandai device sebagai diblokir
                    SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
                    SharedPreferences.Editor editor = prefs.edit();
                    editor.putBoolean(KEY_DEVICE_BLOCKED, true);
                    editor.apply();
                } else {
                    Log.e(TAG, "Gagal melaporkan penggunaan Fake GPS: " + response.message());
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "Error saat melaporkan penggunaan Fake GPS: " + t.getMessage());
            }
        });
    }

    /**
     * Periksa apakah device diblokir
     */
    private void checkDeviceBlocked() {
        // Periksa status blokir lokal
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        boolean isBlocked = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);

        // Dapatkan NIK dari SharedPreferences
        rememberedNik = prefs.getString(KEY_REMEMBERED_NIK, "");

        // Jika NIK tersedia, selalu periksa status blokir di server terlebih dahulu
        if (!rememberedNik.isEmpty() && deviceId != null) {
            // Jika device diblokir secara lokal, tampilkan peringatan terlebih dahulu
            // tapi tetap periksa status di server untuk memastikan status terbaru
            if (isBlocked) {
                showDeviceBlockedWarning();
            }

            // Selalu periksa status di server untuk mendapatkan status terbaru
            Log.d(TAG, "Memeriksa status blokir device di server saat aplikasi dibuka");
            checkDeviceStatusFromServer(deviceId, rememberedNik);
        } else if (isBlocked) {
            // Jika NIK tidak tersedia tapi device diblokir secara lokal, tampilkan peringatan
            showDeviceBlockedWarning();
        }
    }

    /**
     * Periksa status device dari server menggunakan API baru
     *
     * @param deviceId ID perangkat yang akan diperiksa
     * @param nik NIK pengguna (opsional)
     */
    private void checkDeviceStatusFromServer(String deviceId, String nik) {
        AbsensiApiService apiService = ApiClient.getClient().create(AbsensiApiService.class);

        // Gunakan API baru get_device_status.php
        DeviceStatusRequest request = new DeviceStatusRequest(deviceId, nik);
        Call<DeviceStatusResponse> call = apiService.getDeviceStatus(request);

        call.enqueue(new Callback<DeviceStatusResponse>() {
            @Override
            public void onResponse(Call<DeviceStatusResponse> call, Response<DeviceStatusResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    DeviceStatusResponse deviceStatusResponse = response.body();

                    // Dapatkan status device dari respons
                    String deviceStatus = deviceStatusResponse.getStatus();
                    boolean isBlocked = deviceStatusResponse.isDeviceBlocked();

                    Log.d(TAG, "Status device dari API baru: " + deviceStatus +
                          " (" + (isBlocked ? "diblokir" : "diizinkan") + ")");

                    // Dapatkan SharedPreferences
                    SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
                    SharedPreferences.Editor editor = prefs.edit();

                    if (isBlocked) {
                        // Status "blocked" = perangkat masih diblokir
                        editor.putBoolean(KEY_DEVICE_BLOCKED, true);
                        editor.apply();

                        showDeviceBlockedWarning();
                    } else {
                        // Status "allowed" = perangkat sudah diizinkan
                        boolean wasBlockedLocally = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);

                        if (wasBlockedLocally) {
                            // Jika sebelumnya diblokir, reset status
                            editor.putBoolean(KEY_DEVICE_BLOCKED, false);
                            editor.apply();

                            Log.d(TAG, "Status blokir device direset: device sudah diizinkan kembali");
                            Toast.makeText(MainActivity.this, "Perangkat Anda telah diizinkan kembali", Toast.LENGTH_LONG).show();

                            // Refresh halaman web jika sedang aktif
                            if (webView != null) {
                                webView.reload();
                            }
                        }
                    }
                } else {
                    // Jika API baru gagal, coba metode lama sebagai fallback
                    Log.d(TAG, "API baru gagal: " + (response.errorBody() != null ? response.errorBody().toString() : "unknown error"));
                    checkDeviceBlockedOnServer(nik, deviceId);
                }
            }

            @Override
            public void onFailure(Call<DeviceStatusResponse> call, Throwable t) {
                Log.e(TAG, "Error saat memeriksa status device dengan API baru: " + t.getMessage());

                // Jika API baru gagal, coba metode lama sebagai fallback
                Log.d(TAG, "API baru gagal, mencoba metode lama sebagai fallback");
                checkDeviceBlockedOnServer(nik, deviceId);
            }
        });
    }

    /**
     * Periksa status blokir device di server dengan metode lama
     *
     * Status dari server:
     * - active = perangkat masih diblokir (tidak diizinkan)
     * - inactive = perangkat sudah diizinkan (tidak diblokir lagi)
     */
    private void checkDeviceBlockedOnServer(String nik, String deviceId) {
        AbsensiApiService apiService = ApiClient.getClient().create(AbsensiApiService.class);

        // Gunakan metode POST baru dengan CekDeviceRequest
        CekDeviceRequest request = new CekDeviceRequest(deviceId, nik);
        Call<ApiResponse> call = apiService.cekDevicePost(request);

        call.enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    // Dapatkan status dari server
                    boolean isActive = response.body().isStatus();
                    Log.d(TAG, "Status device dari server: " + (isActive ? "active (diblokir)" : "inactive (diizinkan)"));

                    // Dapatkan SharedPreferences
                    SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
                    SharedPreferences.Editor editor = prefs.edit();

                    if (isActive) {
                        // Status active = perangkat masih diblokir
                        editor.putBoolean(KEY_DEVICE_BLOCKED, true);
                        editor.apply();

                        showDeviceBlockedWarning();
                    } else {
                        // Status inactive = perangkat sudah diizinkan
                        boolean wasBlockedLocally = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);

                        if (wasBlockedLocally) {
                            // Jika sebelumnya diblokir, reset status
                            editor.putBoolean(KEY_DEVICE_BLOCKED, false);
                            editor.apply();

                            Log.d(TAG, "Status blokir device direset: device sudah diizinkan kembali");
                            Toast.makeText(MainActivity.this, "Perangkat Anda telah diizinkan kembali", Toast.LENGTH_LONG).show();

                            // Refresh halaman web jika sedang aktif
                            if (webView != null) {
                                webView.reload();
                            }
                        }
                    }
                } else {
                    // Jika metode POST gagal, coba metode GET sebagai fallback
                    Log.d(TAG, "Metode POST gagal, mencoba metode GET sebagai fallback");
                    Call<ApiResponse> fallbackCall = apiService.cekDevice(nik, deviceId);
                    fallbackCall.enqueue(new Callback<ApiResponse>() {
                        @Override
                        public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                            if (response.isSuccessful() && response.body() != null) {
                                // Dapatkan status dari server
                                boolean isActive = response.body().isStatus();
                                Log.d(TAG, "Status device dari server (fallback): " + (isActive ? "active (diblokir)" : "inactive (diizinkan)"));

                                // Dapatkan SharedPreferences
                                SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
                                SharedPreferences.Editor editor = prefs.edit();

                                if (isActive) {
                                    // Status active = perangkat masih diblokir
                                    editor.putBoolean(KEY_DEVICE_BLOCKED, true);
                                    editor.apply();

                                    showDeviceBlockedWarning();
                                } else {
                                    // Status inactive = perangkat sudah diizinkan
                                    boolean wasBlockedLocally = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);

                                    if (wasBlockedLocally) {
                                        // Jika sebelumnya diblokir, reset status
                                        editor.putBoolean(KEY_DEVICE_BLOCKED, false);
                                        editor.apply();

                                        Log.d(TAG, "Status blokir device direset (fallback): device sudah diizinkan kembali");
                                        Toast.makeText(MainActivity.this, "Perangkat Anda telah diizinkan kembali", Toast.LENGTH_LONG).show();

                                        // Refresh halaman web jika sedang aktif
                                        if (webView != null) {
                                            webView.reload();
                                        }
                                    }
                                }
                            } else {
                                Log.e(TAG, "Gagal memeriksa status blokir device (fallback): " + response.message());
                            }
                        }

                        @Override
                        public void onFailure(Call<ApiResponse> call, Throwable t) {
                            Log.e(TAG, "Error saat memeriksa status blokir device (fallback): " + t.getMessage());
                        }
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "Error saat memeriksa status blokir device: " + t.getMessage());

                // Jika metode POST gagal, coba metode GET sebagai fallback
                Log.d(TAG, "Metode POST gagal, mencoba metode GET sebagai fallback");
                Call<ApiResponse> fallbackCall = apiService.cekDevice(nik, deviceId);
                fallbackCall.enqueue(new Callback<ApiResponse>() {
                    @Override
                    public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            // Dapatkan status dari server
                            boolean isActive = response.body().isStatus();
                            Log.d(TAG, "Status device dari server (fallback): " + (isActive ? "active (diblokir)" : "inactive (diizinkan)"));

                            // Dapatkan SharedPreferences
                            SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
                            SharedPreferences.Editor editor = prefs.edit();

                            if (isActive) {
                                // Status active = perangkat masih diblokir
                                editor.putBoolean(KEY_DEVICE_BLOCKED, true);
                                editor.apply();

                                showDeviceBlockedWarning();
                            } else {
                                // Status inactive = perangkat sudah diizinkan
                                boolean wasBlockedLocally = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);

                                if (wasBlockedLocally) {
                                    // Jika sebelumnya diblokir, reset status
                                    editor.putBoolean(KEY_DEVICE_BLOCKED, false);
                                    editor.apply();

                                    Log.d(TAG, "Status blokir device direset (fallback): device sudah diizinkan kembali");
                                    Toast.makeText(MainActivity.this, "Perangkat Anda telah diizinkan kembali", Toast.LENGTH_LONG).show();

                                    // Refresh halaman web jika sedang aktif
                                    if (webView != null) {
                                        webView.reload();
                                    }
                                }
                            }
                        } else {
                            Log.e(TAG, "Gagal memeriksa status blokir device (fallback): " + response.message());
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse> call, Throwable t) {
                        Log.e(TAG, "Error saat memeriksa status blokir device (fallback): " + t.getMessage());
                    }
                });
            }
        });
    }

    /**
     * Tampilkan peringatan device diblokir
     */
    private void showDeviceBlockedWarning() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Perangkat Diblokir");
        builder.setMessage("Perangkat ini telah diblokir karena terdeteksi penggunaan Fake GPS atau alasan keamanan lainnya. " +
                "Silakan hubungi administrator untuk informasi lebih lanjut.");
        builder.setPositiveButton("OK", (dialog, which) -> {
            dialog.dismiss();
            finish();
        });
        builder.setCancelable(false);
        builder.show();
    }

    /**
     * Reset status blokir device secara lokal
     * Metode ini bisa dipanggil dari JavaScript atau untuk debugging
     */
    public void resetDeviceBlockStatus() {
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_DEVICE_BLOCKED, false);
        editor.apply();

        Log.d(TAG, "Status blokir device direset secara manual");
        Toast.makeText(this, "Status blokir device direset", Toast.LENGTH_SHORT).show();
    }

    /**
     * Reset status first time untuk menampilkan welcome screen lagi
     * Metode ini hanya untuk debugging
     */
    public void resetFirstTimeStatus() {
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_FIRST_TIME, true);
        editor.apply();

        Log.d(TAG, "Status first time direset secara manual");
        Toast.makeText(this, "Welcome screen akan ditampilkan saat aplikasi dibuka kembali", Toast.LENGTH_LONG).show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Bersihkan handler
        if (periodicCheckHandler != null) {
            periodicCheckHandler.removeCallbacksAndMessages(null);
        }

        webView.destroy();

        // Service tracking lokasi tetap berjalan meskipun activity dihancurkan
        // Jika ingin menghentikan service, uncomment kode di bawah ini
        // stopService(locationTrackingServiceIntent);
    }
}