package com.webblogtoolbars.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

/**
 * Model data untuk absensi yang akan disimpan di database lokal
 */
@Entity(tableName = "absensi_data")
public class AbsensiData {
    
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private String nik;
    private String nama;
    private String tanggal;
    private String jam;
    private String jenisAbsen;
    private String foto;
    private boolean isSynced;
    
    public AbsensiData(String nik, String nama, String tanggal, String jam, String jenisAbsen, String foto) {
        this.nik = nik;
        this.nama = nama;
        this.tanggal = tanggal;
        this.jam = jam;
        this.jenisAbsen = jenisAbsen;
        this.foto = foto;
        this.isSynced = false;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getNik() {
        return nik;
    }
    
    public void setNik(String nik) {
        this.nik = nik;
    }
    
    public String getNama() {
        return nama;
    }
    
    public void setNama(String nama) {
        this.nama = nama;
    }
    
    public String getTanggal() {
        return tanggal;
    }
    
    public void setTanggal(String tanggal) {
        this.tanggal = tanggal;
    }
    
    public String getJam() {
        return jam;
    }
    
    public void setJam(String jam) {
        this.jam = jam;
    }
    
    public String getJenisAbsen() {
        return jenisAbsen;
    }
    
    public void setJenisAbsen(String jenisAbsen) {
        this.jenisAbsen = jenisAbsen;
    }
    
    public String getFoto() {
        return foto;
    }
    
    public void setFoto(String foto) {
        this.foto = foto;
    }
    
    public boolean isSynced() {
        return isSynced;
    }
    
    public void setSynced(boolean synced) {
        isSynced = synced;
    }
}
