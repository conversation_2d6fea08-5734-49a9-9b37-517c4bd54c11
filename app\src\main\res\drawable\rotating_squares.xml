<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Kotak 1 (kiri atas) - Biru -->
    <item>
        <rotate
            android:fromDegrees="0"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="0">
            <shape android:shape="rectangle">
                <solid android:color="@color/colorBlue" />
                <size
                    android:width="10dp"
                    android:height="10dp" />
                <corners android:radius="2dp" />
            </shape>
        </rotate>
    </item>

    <!-- Kotak 2 (kanan atas) - Gradasi Biru ke Pink -->
    <item>
        <rotate
            android:fromDegrees="90"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="90">
            <shape android:shape="rectangle">
                <gradient
                    android:startColor="@color/colorBlue"
                    android:endColor="@color/colorPink"
                    android:angle="45" />
                <size
                    android:width="10dp"
                    android:height="10dp" />
                <corners android:radius="2dp" />
            </shape>
        </rotate>
    </item>

    <!-- Kotak 3 (kanan bawah) - Pink -->
    <item>
        <rotate
            android:fromDegrees="180"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="180">
            <shape android:shape="rectangle">
                <solid android:color="@color/colorPink" />
                <size
                    android:width="10dp"
                    android:height="10dp" />
                <corners android:radius="2dp" />
            </shape>
        </rotate>
    </item>

    <!-- Kotak 4 (kiri bawah) - Gradasi Pink ke Biru -->
    <item>
        <rotate
            android:fromDegrees="270"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="270">
            <shape android:shape="rectangle">
                <gradient
                    android:startColor="@color/colorPink"
                    android:endColor="@color/colorBlue"
                    android:angle="45" />
                <size
                    android:width="10dp"
                    android:height="10dp" />
                <corners android:radius="2dp" />
            </shape>
        </rotate>
    </item>
</layer-list>
