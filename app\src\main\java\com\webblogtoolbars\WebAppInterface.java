package com.webblogtoolbars;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;
import android.webkit.JavascriptInterface;

import com.webblogtoolbars.service.LocationTrackingService;

/**
 * Interface untuk komunikasi antara WebView dan Android
 */
public class WebAppInterface {
    private Context context;
    private static final String TAG = "WebAppInterface";
    private static final String PREF_NAME = "AbsensikuPrefs";
    private static final String KEY_REMEMBERED_NIK = "rememberedNik";
    private static final String KEY_DEVICE_BLOCKED = "deviceBlocked";
    private static final String KEY_FIRST_TIME = "firstTime";

    public WebAppInterface(Context context) {
        this.context = context;
    }

    /**
     * Menyimpan NIK yang digunakan untuk login
     * Dipanggil dari JavaScript di halaman web
     */
    @JavascriptInterface
    public void saveNik(String nik) {
        if (nik != null && !nik.isEmpty()) {
            Log.d(TAG, "Menyimpan NIK: " + nik);
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_REMEMBERED_NIK, nik);
            editor.apply();

            // Mulai service tracking lokasi setelah NIK tersimpan
            startLocationTrackingService();
        }
    }

    /**
     * Mulai service tracking lokasi
     */
    private void startLocationTrackingService() {
        // Buat intent untuk service
        Intent serviceIntent = new Intent(context, LocationTrackingService.class);

        // Mulai service
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent);
        } else {
            context.startService(serviceIntent);
        }

        Log.d(TAG, "Service tracking lokasi dimulai dari WebAppInterface");
    }

    /**
     * Mendapatkan NIK yang tersimpan
     * Dipanggil dari JavaScript di halaman web
     */
    @JavascriptInterface
    public String getNik() {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        String nik = prefs.getString(KEY_REMEMBERED_NIK, "");
        Log.d(TAG, "Mendapatkan NIK: " + nik);
        return nik;
    }

    /**
     * Memeriksa apakah device diblokir
     * Dipanggil dari JavaScript di halaman web
     */
    @JavascriptInterface
    public boolean isDeviceBlocked() {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        boolean isBlocked = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);
        Log.d(TAG, "Memeriksa status blokir device: " + isBlocked);
        return isBlocked;
    }

    /**
     * Reset status blokir device
     * Dipanggil dari JavaScript di halaman web (hanya untuk admin)
     */
    @JavascriptInterface
    public void resetDeviceBlockStatus() {
        if (context instanceof MainActivity) {
            ((MainActivity) context).resetDeviceBlockStatus();
        } else {
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putBoolean(KEY_DEVICE_BLOCKED, false);
            editor.apply();
            Log.d(TAG, "Status blokir device direset dari JavaScript");
        }
    }

    /**
     * Reset status first time untuk menampilkan welcome screen lagi
     * Dipanggil dari JavaScript di halaman web (hanya untuk admin)
     */
    @JavascriptInterface
    public void resetFirstTimeStatus() {
        if (context instanceof MainActivity) {
            ((MainActivity) context).resetFirstTimeStatus();
        } else {
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putBoolean(KEY_FIRST_TIME, true);
            editor.apply();
            Log.d(TAG, "Status first time direset dari JavaScript");
        }
    }
}
