package com.webblogtoolbars.util;

import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

/**
 * Utility class untuk mendeteksi fake GPS
 */
public class MockLocationDetector {
    
    private static final String TAG = "MockLocationDetector";
    
    /**
     * Memeriksa apakah lokasi palsu diaktifkan di pengaturan developer
     * @param context Context aplikasi
     * @return true jika lokasi palsu diaktifkan
     */
    public static boolean isMockLocationEnabled(Context context) {
        boolean isMockLocation = false;
        try {
            // Untuk Android M dan yang lebih baru
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
                if (locationManager != null) {
                    try {
                        // Coba dapatkan lokasi terakhir
                        Location location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
                        if (location != null) {
                            isMockLocation = location.isFromMockProvider();
                        }
                        
                        // Jika tidak ada lokasi terakhir, coba provider lain
                        if (location == null) {
                            location = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
                            if (location != null) {
                                isMockLocation = location.isFromMockProvider();
                            }
                        }
                    } catch (SecurityException e) {
                        Log.e(TAG, "Error checking mock location: " + e.getMessage());
                    }
                }
            } else {
                // Untuk Android versi lama
                isMockLocation = !Settings.Secure.getString(context.getContentResolver(),
                        Settings.Secure.ALLOW_MOCK_LOCATION).equals("0");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking mock location: " + e.getMessage());
        }
        
        return isMockLocation;
    }
    
    /**
     * Memeriksa apakah aplikasi mock location terinstal
     * @param context Context aplikasi
     * @return true jika aplikasi mock location terinstal
     */
    public static boolean areMockLocationAppsInstalled(Context context) {
        boolean mockAppsInstalled = false;
        
        try {
            // Untuk Android M dan yang lebih baru
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                mockAppsInstalled = Settings.Secure.getInt(context.getContentResolver(),
                        Settings.Secure.ALLOW_MOCK_LOCATION, 0) != 0;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking mock location apps: " + e.getMessage());
        }
        
        return mockAppsInstalled;
    }
}
