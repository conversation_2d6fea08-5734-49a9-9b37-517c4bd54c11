package com.webblogtoolbars;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.work.Constraints;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;
import com.google.android.material.snackbar.Snackbar;

import com.google.android.material.textfield.TextInputEditText;
import com.webblogtoolbars.database.AbsensiDatabase;
import com.webblogtoolbars.model.AbsensiData;
import com.webblogtoolbars.util.ImageUtil;
import com.webblogtoolbars.util.NetworkUtil;
import com.webblogtoolbars.worker.SyncWorker;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class OfflineAbsensiActivity extends AppCompatActivity {

    private static final String TAG = "OfflineAbsensiActivity";
    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final int REQUEST_CAMERA_PERMISSION = 2;

    private TextInputEditText etNik, etNama;
    private RadioGroup rgJenisAbsen;
    private RadioButton rbMasuk, rbKeluar;
    private ImageView ivFoto;
    private Button btnAmbilFoto, btnSubmit;
    private TextView tvStatus, tvPendingData;

    private String currentPhotoPath;
    private String base64Image;
    private AbsensiDatabase database;
    private ExecutorService executorService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_offline_absensi);

        // Inisialisasi komponen UI
        etNik = findViewById(R.id.etNik);
        etNama = findViewById(R.id.etNama);
        rgJenisAbsen = findViewById(R.id.rgJenisAbsen);
        rbMasuk = findViewById(R.id.rbMasuk);
        rbKeluar = findViewById(R.id.rbKeluar);
        ivFoto = findViewById(R.id.ivFoto);
        btnAmbilFoto = findViewById(R.id.btnAmbilFoto);
        btnSubmit = findViewById(R.id.btnSubmit);
        tvStatus = findViewById(R.id.tvStatus);
        tvPendingData = findViewById(R.id.tvPendingData);

        // Inisialisasi database dan executor
        database = AbsensiDatabase.getDatabase(this);
        executorService = Executors.newSingleThreadExecutor();

        // Set listener untuk tombol ambil foto
        btnAmbilFoto.setOnClickListener(v -> {
            if (checkCameraPermission()) {
                dispatchTakePictureIntent();
            } else {
                requestCameraPermission();
            }
        });

        // Set listener untuk tombol submit
        btnSubmit.setOnClickListener(v -> {
            if (validateInput()) {
                saveAbsensi();
            }
        });



        // Update jumlah data tertunda
        updatePendingDataCount();
    }

    private boolean validateInput() {
        if (etNik.getText().toString().trim().isEmpty()) {
            etNik.setError("NIK tidak boleh kosong");
            return false;
        }

        if (etNama.getText().toString().trim().isEmpty()) {
            etNama.setError("Nama tidak boleh kosong");
            return false;
        }

        if (base64Image == null) {
            Toast.makeText(this, "Silakan ambil foto terlebih dahulu", Toast.LENGTH_SHORT).show();
            return false;
        }

        return true;
    }

    private void saveAbsensi() {
        String nik = etNik.getText().toString().trim();
        String nama = etNama.getText().toString().trim();
        String jenisAbsen = rbMasuk.isChecked() ? "masuk" : "keluar";

        // Format tanggal dan jam
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
        String tanggal = dateFormat.format(new Date());
        String jam = timeFormat.format(new Date());

        // Buat objek AbsensiData
        AbsensiData absensiData = new AbsensiData(nik, nama, tanggal, jam, jenisAbsen, base64Image);

        // Simpan ke database
        executorService.execute(() -> {
            long id = database.absensiDao().insert(absensiData);
            runOnUiThread(() -> {
                if (id > 0) {
                    Toast.makeText(OfflineAbsensiActivity.this, "Data berhasil disimpan dan akan dikirim ke server saat online", Toast.LENGTH_LONG).show();

                    // Tunggu sebentar untuk memastikan pesan toast terlihat
                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                        // Tutup aplikasi
                        finishAffinity();
                    }, 2000);
                } else {
                    Toast.makeText(OfflineAbsensiActivity.this, "Gagal menyimpan data", Toast.LENGTH_SHORT).show();
                }
            });
        });
    }

    private void resetForm() {
        etNik.setText("");
        etNama.setText("");
        rbMasuk.setChecked(true);
        ivFoto.setImageResource(R.drawable.ic_camera);
        base64Image = null;
        btnAmbilFoto.setVisibility(View.VISIBLE);
    }

    private void updatePendingDataCount() {
        executorService.execute(() -> {
            // Hapus data lama yang sudah disinkronkan (lebih dari 7 hari)
            try {
                database.absensiDao().deleteOldSyncedData();
                Log.d(TAG, "Data lama yang sudah disinkronkan berhasil dihapus");
            } catch (Exception e) {
                Log.e(TAG, "Error saat menghapus data lama: " + e.getMessage());
            }

            // Ambil jumlah data yang belum disinkronkan
            List<AbsensiData> unsyncedData = database.absensiDao().getUnsyncedAbsensi();
            runOnUiThread(() -> {
                if (unsyncedData.isEmpty()) {
                    tvPendingData.setText("Tidak ada data tertunda");
                    tvStatus.setText("Semua data telah disinkronisasi dengan server");
                } else {
                    tvPendingData.setText("Data tertunda: " + unsyncedData.size());
                    tvStatus.setText("Data akan disimpan secara lokal dan akan dikirim ke server saat koneksi tersedia.");
                }
            });
        });
    }



    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
    }

    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, REQUEST_CAMERA_PERMISSION);
    }

    private void dispatchTakePictureIntent() {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
            File photoFile = null;
            try {
                photoFile = createImageFile();
            } catch (IOException ex) {
                Log.e(TAG, "Error creating image file", ex);
            }

            if (photoFile != null) {
                Uri photoURI = FileProvider.getUriForFile(this,
                        "com.webblogtoolbars.fileprovider",
                        photoFile);
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
            }
        }
    }

    private File createImageFile() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        File image = File.createTempFile(
                imageFileName,
                ".jpg",
                storageDir
        );

        currentPhotoPath = image.getAbsolutePath();
        return image;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_IMAGE_CAPTURE && resultCode == RESULT_OK) {
            File imgFile = new File(currentPhotoPath);
            if (imgFile.exists()) {
                Bitmap bitmap = BitmapFactory.decodeFile(currentPhotoPath);
                // Kompres bitmap untuk mengurangi ukuran
                bitmap = ImageUtil.compressBitmap(bitmap, 70);
                ivFoto.setImageBitmap(bitmap);
                base64Image = ImageUtil.bitmapToBase64(bitmap);
                btnAmbilFoto.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                dispatchTakePictureIntent();
            } else {
                Toast.makeText(this, "Izin kamera diperlukan untuk mengambil foto", Toast.LENGTH_SHORT).show();
            }
        }
    }


}
