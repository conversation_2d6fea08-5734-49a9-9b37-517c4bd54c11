package com.webblogtoolbars.database;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.webblogtoolbars.model.AbsensiData;

/**
 * Database Room untuk menyimpan data absensi
 */
@Database(entities = {AbsensiData.class}, version = 1, exportSchema = false)
public abstract class AbsensiDatabase extends RoomDatabase {

    public abstract AbsensiDao absensiDao();

    private static AbsensiDatabase INSTANCE;

    public static AbsensiDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (AbsensiDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            AbsensiDatabase.class,
                            "absensi_database"
                        )
                        .fallbackToDestructiveMigration() // Untuk menghindari crash saat skema berubah
                        .build();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * Metode untuk menghapus cache database
     * Ini akan memaksa Room untuk mengambil data baru dari database
     */
    public void clearAllTables() {
        if (INSTANCE != null && INSTANCE.isOpen()) {
            // Bersihkan cache tanpa menghapus data
            INSTANCE.clearAllTables();
        }
    }
}
