<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Progress Bar floating dengan animasi kotak 4 berputar -->
    <RelativeLayout
        android:id="@+id/progressBarLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80FFFFFF"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@android:color/white"
            android:elevation="4dp"
            android:minWidth="120dp"
            android:minHeight="120dp">

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:indeterminate="true"
                android:indeterminateDrawable="@drawable/rotating_squares_animation" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Loading..."
                android:textColor="@color/colorBlue"
                android:textStyle="bold" />

        </LinearLayout>

    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/loadingLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@android:color/white">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Sedang memeriksa koneksi"
                android:textColor="@android:color/black" />

        </LinearLayout>

    </RelativeLayout>
</RelativeLayout>
