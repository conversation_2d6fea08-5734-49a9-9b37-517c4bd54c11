<html>
<head>
<title>MainActivity.java</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style type="text/css">
.s0 { color: #cc7832;}
.s1 { color: #a9b7c6;}
.s2 { color: #808080;}
.s3 { color: #6a8759;}
</style>
</head>
<body bgcolor="#2b2b2b">
<table CELLSPACING=0 CELLPADDING=5 COLS=1 WIDTH="100%" BGCOLOR="#606060" >
<tr><td><center>
<font face="Arial, Helvetica" color="#000000">
MainActivity.java</font>
</center></td></tr></table>
<pre><span class="s0">package </span><span class="s1">com.webblogtoolbars</span><span class="s0">;</span>

<span class="s0">import </span><span class="s1">androidx.appcompat.app.AppCompatActivity</span><span class="s0">;</span>
<span class="s0">import </span><span class="s1">android.os.Bundle</span><span class="s0">;</span>
<span class="s0">import </span><span class="s1">android.webkit.WebSettings</span><span class="s0">;</span>
<span class="s0">import </span><span class="s1">android.webkit.WebView</span><span class="s0">;</span>
<span class="s0">import </span><span class="s1">android.webkit.WebViewClient</span><span class="s0">;</span>

<span class="s0">public class </span><span class="s1">MainActivity </span><span class="s0">extends </span><span class="s1">AppCompatActivity {</span>
    <span class="s0">private </span><span class="s1">WebView webView</span><span class="s0">;</span>
    <span class="s1">@Override</span>
    <span class="s0">protected void </span><span class="s1">onCreate(Bundle savedInstanceState) {</span>
        <span class="s0">super</span><span class="s1">.onCreate(savedInstanceState)</span><span class="s0">;</span>
        <span class="s1">setContentView(R.layout.activity_main)</span><span class="s0">;</span>
        <span class="s1">webView = findViewById(R.id.webView)</span><span class="s0">;</span>

        <span class="s2">// Enable JavaScript (optional)</span>
        <span class="s1">WebSettings webSettings = webView.getSettings()</span><span class="s0">;</span>
        <span class="s1">webSettings.setJavaScriptEnabled(</span><span class="s0">true</span><span class="s1">)</span><span class="s0">;</span>

        <span class="s2">// Load a webpage</span>
        <span class="s1">webView.loadUrl(</span><span class="s3">&quot;https://www.javasetid.com/&quot;</span><span class="s1">)</span><span class="s0">;</span>

        <span class="s2">// Ensure links open within the WebView</span>
        <span class="s1">webView.setWebViewClient(</span><span class="s0">new </span><span class="s1">WebViewClient())</span><span class="s0">;</span>
    <span class="s1">}</span>

    <span class="s2">// Handle back button navigation in the WebView</span>
    <span class="s1">@Override</span>
    <span class="s0">public void </span><span class="s1">onBackPressed() {</span>
        <span class="s0">if </span><span class="s1">(webView.canGoBack()) {</span>
            <span class="s1">webView.goBack()</span><span class="s0">;</span>
        <span class="s1">} </span><span class="s0">else </span><span class="s1">{</span>
            <span class="s0">super</span><span class="s1">.onBackPressed()</span><span class="s0">;</span>
        <span class="s1">}</span>
    <span class="s1">}</span>
<span class="s1">}</span>
</pre>
</body>
</html>