package com.webblogtoolbars;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Activity untuk menampilkan persetujuan penggunaan aplikasi
 * Ditampilkan setelah welcome screen saat pertama kali aplikasi diinstal
 */
public class AgreementActivity extends AppCompatActivity {

    private static final String PREF_NAME = "AbsensikuPrefs";
    private static final String KEY_AGREEMENT_ACCEPTED = "agreementAccepted";
    private static final String KEY_FIRST_TIME = "firstTime";

    private Button btnAgree, btnDisagree;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_agreement);

        // Inisialisasi views
        btnAgree = findViewById(R.id.btnAgree);
        btnDisagree = findViewById(R.id.btnDisagree);

        // Set listener untuk tombol setuju
        btnAgree.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Simpan status persetujuan
                saveAgreementStatus(true);
                
                // Tampilkan pesan
                Toast.makeText(AgreementActivity.this, "Terima kasih atas persetujuan Anda", Toast.LENGTH_SHORT).show();
                
                // Lanjut ke MainActivity
                startMainActivity();
            }
        });

        // Set listener untuk tombol tidak setuju
        btnDisagree.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Tampilkan dialog konfirmasi
                showDisagreeDialog();
            }
        });
    }

    /**
     * Simpan status persetujuan di SharedPreferences
     */
    private void saveAgreementStatus(boolean accepted) {
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_AGREEMENT_ACCEPTED, accepted);
        
        // Tandai bahwa aplikasi sudah pernah dibuka (tidak perlu menampilkan welcome screen lagi)
        editor.putBoolean(KEY_FIRST_TIME, false);
        
        editor.apply();
    }

    /**
     * Tampilkan dialog konfirmasi saat pengguna tidak setuju
     */
    private void showDisagreeDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Konfirmasi");
        builder.setMessage("Anda harus menyetujui ketentuan penggunaan untuk menggunakan aplikasi ini. " +
                "Apakah Anda yakin ingin keluar dari aplikasi?");
        builder.setPositiveButton("Ya, Keluar", (dialog, which) -> {
            dialog.dismiss();
            finish();
        });
        builder.setNegativeButton("Baca Lagi", (dialog, which) -> {
            dialog.dismiss();
        });
        builder.setCancelable(false);
        builder.show();
    }

    /**
     * Lanjut ke MainActivity
     */
    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
}
