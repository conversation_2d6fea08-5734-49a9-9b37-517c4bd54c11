package com.webblogtoolbars.repository;

import android.app.Application;
import android.os.AsyncTask;

import androidx.lifecycle.LiveData;

import com.webblogtoolbars.database.AbsensiDao;
import com.webblogtoolbars.database.AbsensiDatabase;
import com.webblogtoolbars.model.AbsensiData;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Repository untuk mengelola data absensi
 */
public class AbsensiRepository {
    
    private AbsensiDao absensiDao;
    private LiveData<List<AbsensiData>> allAbsensi;
    private ExecutorService executorService;
    
    public AbsensiRepository(Application application) {
        AbsensiDatabase database = AbsensiDatabase.getDatabase(application);
        absensiDao = database.absensiDao();
        allAbsensi = absensiDao.getAllAbsensi();
        executorService = Executors.newSingleThreadExecutor();
    }
    
    public LiveData<List<AbsensiData>> getAllAbsensi() {
        return allAbsensi;
    }
    
    public void insert(AbsensiData absensiData) {
        executorService.execute(() -> {
            absensiDao.insert(absensiData);
        });
    }
    
    public void update(AbsensiData absensiData) {
        executorService.execute(() -> {
            absensiDao.update(absensiData);
        });
    }
    
    public void deleteById(int id) {
        executorService.execute(() -> {
            absensiDao.deleteById(id);
        });
    }
    
    public List<AbsensiData> getUnsyncedAbsensi() {
        return absensiDao.getUnsyncedAbsensi();
    }
}
