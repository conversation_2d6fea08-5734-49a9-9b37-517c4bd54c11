package com.webblogtoolbars;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.webblogtoolbars.util.DeviceUtils;
import com.webblogtoolbars.util.MockLocationDetector;

public class WelcomeActivity extends AppCompatActivity {

    private static final String PREF_NAME = "AbsensikuPrefs";
    private static final String KEY_FIRST_TIME = "firstTime";
    private static final String KEY_DEVICE_ID = "deviceId";
    private static final String KEY_DEVICE_BLOCKED = "deviceBlocked";
    private static final String KEY_REMEMBERED_NIK = "rememberedNik";
    private static final String KEY_AGREEMENT_ACCEPTED = "agreementAccepted";

    private ImageView ivLogo;
    private TextView tvWelcome, tvAppName, tvVersion;
    private Button btnGetStarted;

    private String deviceId;
    private String rememberedNik;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Periksa apakah ini pertama kali aplikasi dibuka
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        boolean isFirstTime = prefs.getBoolean(KEY_FIRST_TIME, true);
        boolean isAgreementAccepted = prefs.getBoolean(KEY_AGREEMENT_ACCEPTED, false);

        if (!isFirstTime && isAgreementAccepted) {
            // Bukan pertama kali dan persetujuan sudah diterima, langsung ke MainActivity
            startMainActivity();
            return;
        } else if (!isFirstTime && !isAgreementAccepted) {
            // Bukan pertama kali tapi persetujuan belum diterima, tampilkan AgreementActivity
            startAgreementActivity();
            return;
        }

        // Pertama kali, tampilkan welcome screen
        setContentView(R.layout.activity_welcome);

        // Inisialisasi device ID
        initDeviceId();

        // CATATAN PENTING:
        // 1. Pastikan file logo_absen.png dari folder I:\Absensiku\app\src\main\res\mipmap-xxxhdpi
        //    telah disalin ke folder app/src/main/res/mipmap-xxxhdpi di proyek ini
        // 2. Jika logo tidak muncul, berarti file logo_absen.png belum ada di folder mipmap-xxxhdpi
        // 3. Cara menyalin file:
        //    - Buka folder I:\Absensiku\app\src\main\res\mipmap-xxxhdpi
        //    - Salin file logo_absen.png
        //    - Buka folder proyek ini: app/src/main/res/mipmap-xxxhdpi
        //    - Tempel file logo_absen.png ke folder tersebut

        // Inisialisasi views
        ivLogo = findViewById(R.id.ivLogo);
        tvWelcome = findViewById(R.id.tvWelcome);
        tvAppName = findViewById(R.id.tvAppName);
        tvVersion = findViewById(R.id.tvVersion);
        btnGetStarted = findViewById(R.id.btnGetStarted);

        // Set versi aplikasi
        try {
            String versionName = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
            tvVersion.setText("Versi " + versionName);
        } catch (Exception e) {
            tvVersion.setText("Versi 1.0");
        }

        // Set visibility awal
        tvWelcome.setVisibility(View.INVISIBLE);
        tvAppName.setVisibility(View.INVISIBLE);
        btnGetStarted.setVisibility(View.INVISIBLE);
        tvVersion.setVisibility(View.INVISIBLE);

        // Animasi
        Animation fadeIn = AnimationUtils.loadAnimation(this, android.R.anim.fade_in);
        fadeIn.setDuration(600);

        Animation zoomIn = AnimationUtils.loadAnimation(this, android.R.anim.fade_in);
        zoomIn.setDuration(800);

        // Animasi logo dengan zoom in
        ivLogo.setScaleX(0.5f);
        ivLogo.setScaleY(0.5f);
        ivLogo.setAlpha(0f);

        ivLogo.animate()
            .scaleX(1f)
            .scaleY(1f)
            .alpha(1f)
            .setDuration(800)
            .start();

        // Delay untuk animasi teks
        new Handler().postDelayed(() -> {
            tvWelcome.setVisibility(View.VISIBLE);
            tvWelcome.startAnimation(fadeIn);
        }, 500);

        new Handler().postDelayed(() -> {
            tvAppName.setVisibility(View.VISIBLE);
            tvAppName.startAnimation(fadeIn);
        }, 800);

        new Handler().postDelayed(() -> {
            btnGetStarted.setVisibility(View.VISIBLE);
            btnGetStarted.startAnimation(fadeIn);
            tvVersion.setVisibility(View.VISIBLE);
            tvVersion.startAnimation(fadeIn);
        }, 1100);

        // Set listener untuk tombol mulai
        btnGetStarted.setOnClickListener(v -> {
            // Periksa fake GPS
            if (MockLocationDetector.isMockLocationEnabled(this) || MockLocationDetector.areMockLocationAppsInstalled(this)) {
                // Fake GPS terdeteksi, tampilkan peringatan
                showMockLocationWarning();
            } else {
                // Periksa apakah device diblokir
                boolean isBlocked = prefs.getBoolean(KEY_DEVICE_BLOCKED, false);

                if (isBlocked) {
                    // Device diblokir, tampilkan peringatan
                    showDeviceBlockedWarning();
                } else {
                    // Lanjut ke AgreementActivity
                    startAgreementActivity();
                }
            }
        });
    }

    /**
     * Inisialisasi device ID
     */
    private void initDeviceId() {
        // Dapatkan device ID dari SharedPreferences atau generate baru
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        deviceId = prefs.getString(KEY_DEVICE_ID, null);

        if (deviceId == null || deviceId.isEmpty()) {
            // Generate device ID baru
            deviceId = DeviceUtils.getDeviceId(this);

            // Simpan device ID
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_DEVICE_ID, deviceId);
            editor.apply();
        }
    }

    /**
     * Tampilkan peringatan fake GPS
     */
    private void showMockLocationWarning() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Peringatan Keamanan");
        builder.setMessage("Aplikasi mendeteksi penggunaan Fake GPS atau Mock Location. " +
                "Penggunaan aplikasi ini tidak diizinkan dan perangkat Anda akan diblokir.");
        builder.setPositiveButton("OK", (dialog, which) -> {
            dialog.dismiss();
            finish();
        });
        builder.setCancelable(false);
        builder.show();
    }

    /**
     * Tampilkan peringatan device diblokir
     */
    private void showDeviceBlockedWarning() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Perangkat Diblokir");
        builder.setMessage("Perangkat ini telah diblokir karena terdeteksi penggunaan Fake GPS atau alasan keamanan lainnya. " +
                "Silakan hubungi administrator untuk informasi lebih lanjut.");
        builder.setPositiveButton("OK", (dialog, which) -> {
            dialog.dismiss();
            finish();
        });
        builder.setCancelable(false);
        builder.show();
    }

    /**
     * Lanjut ke AgreementActivity
     */
    private void startAgreementActivity() {
        Intent intent = new Intent(this, AgreementActivity.class);
        startActivity(intent);
        finish();
    }

    /**
     * Lanjut ke MainActivity
     */
    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
}
