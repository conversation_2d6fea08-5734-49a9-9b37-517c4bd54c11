package com.webblogtoolbars.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model untuk request cek status device dengan API baru
 */
public class DeviceStatusRequest {
    
    @SerializedName("api_key")
    private String apiKey;
    
    @SerializedName("device_id")
    private String deviceId;
    
    @SerializedName("nik")
    private String nik;
    
    /**
     * Constructor dengan NIK (opsional)
     */
    public DeviceStatusRequest(String deviceId, String nik) {
        this.apiKey = "absensiku_api_key_2023";
        this.deviceId = deviceId;
        this.nik = nik;
    }
    
    /**
     * Constructor tanpa NIK
     */
    public DeviceStatusRequest(String deviceId) {
        this.apiKey = "absensiku_api_key_2023";
        this.deviceId = deviceId;
        this.nik = null; // NIK opsional
    }
    
    public String getApiKey() {
        return apiKey;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public String getNik() {
        return nik;
    }
}
