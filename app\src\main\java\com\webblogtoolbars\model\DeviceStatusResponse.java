package com.webblogtoolbars.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model untuk response API get_device_status.php
 *
 * Format respons:
 * {
 *     "status": "allowed",
 *     "message": "Device diizinkan"
 * }
 *
 * atau
 *
 * {
 *     "status": "blocked",
 *     "message": "Device diblokir"
 * }
 */
public class DeviceStatusResponse {

    @SerializedName("status")
    private String status; // Status device: "allowed" atau "blocked"

    @SerializedName("message")
    private String message; // Pesan dari server

    public String getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    /**
     * Periksa apakah device diblokir berdasarkan status dari server
     * @return true jika device diblokir (status="blocked"), false jika device diizinkan (status="allowed")
     */
    public boolean isDeviceBlocked() {
        // Status "blocked" berarti device diblokir
        return status != null && status.equals("blocked");
    }
}
