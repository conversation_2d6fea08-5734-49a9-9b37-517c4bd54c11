package com.webblogtoolbars.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model untuk request API absensi
 */
public class AbsensiRequest {
    
    @SerializedName("api_key")
    private String apiKey;
    
    @SerializedName("nik")
    private String nik;
    
    @SerializedName("nama")
    private String nama;
    
    @SerializedName("tanggal")
    private String tanggal;
    
    @SerializedName("jam")
    private String jam;
    
    @SerializedName("jenis_absen")
    private String jenisAbsen;
    
    @SerializedName("foto")
    private String foto;
    
    public AbsensiRequest(String apiKey, String nik, String nama, String tanggal, String jam, String jenisAbsen, String foto) {
        this.apiKey = apiKey;
        this.nik = nik;
        this.nama = nama;
        this.tanggal = tanggal;
        this.jam = jam;
        this.jenisAbsen = jenisAbsen;
        this.foto = foto;
    }
    
    // Factory method untuk membuat AbsensiRequest dari AbsensiData
    public static AbsensiRequest fromAbsensiData(AbsensiData absensiData) {
        return new AbsensiRequest(
            "absensiku_api_key_2023",
            absensiData.getNik(),
            absensiData.getNama(),
            absensiData.getTanggal(),
            absensiData.getJam(),
            absensiData.getJenisAbsen(),
            absensiData.getFoto()
        );
    }
}
