package com.webblogtoolbars.receiver;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import com.webblogtoolbars.service.LocationTrackingService;
import com.webblogtoolbars.reminder.AttendanceReminderManager;

/**
 * Receiver untuk memulai service saat boot
 */
public class BootReceiver extends BroadcastReceiver {

    private static final String TAG = "BootReceiver";
    private static final String PREF_NAME = "AbsensikuPrefs";
    private static final String KEY_REMEMBERED_NIK = "rememberedNik";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent.getAction() != null &&
            (intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED) ||
             intent.getAction().equals("android.intent.action.QUICKBOOT_POWERON"))) {

            Log.d(TAG, "Boot completed, starting location tracking service");

            // Cek apakah NIK tersedia
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            String rememberedNik = prefs.getString(KEY_REMEMBERED_NIK, "");



            // Jadwalkan pengingat absensi jika izin notifikasi diberikan
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU ||
                    context.checkSelfPermission(Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
                AttendanceReminderManager reminderManager = new AttendanceReminderManager(context);
                reminderManager.scheduleAllReminders();
                Log.d(TAG, "Pengingat absensi dijadwalkan setelah boot");
            } else {
                Log.d(TAG, "Izin notifikasi tidak diberikan, tidak menjadwalkan pengingat");
            }

            if (!rememberedNik.isEmpty()) {
                // Mulai LocationTrackingService jika NIK tersedia
                Intent locationServiceIntent = new Intent(context, LocationTrackingService.class);

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(locationServiceIntent);
                } else {
                    context.startService(locationServiceIntent);
                }

                Log.d(TAG, "Location tracking service started after boot");
            } else {
                Log.d(TAG, "NIK tidak tersedia, tidak memulai location tracking service");
            }
        }
    }
}
