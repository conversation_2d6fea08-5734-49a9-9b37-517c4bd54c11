package com.webblogtoolbars.util;

import android.content.Context;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
 * Utility class untuk mendapatkan informasi device
 */
public class DeviceUtils {
    
    private static final String TAG = "DeviceUtils";
    
    /**
     * Mendapatkan unique device ID
     * @param context Context aplikasi
     * @return Device ID
     */
    public static String getDeviceId(Context context) {
        String deviceId = "";
        
        try {
            // Gunakan Android ID sebagai basis
            String androidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            
            // Jika Android ID tidak tersedia atau merupakan ID yang diketahui tidak unik
            if (androidId == null || androidId.equals("9774d56d682e549c") || androidId.length() < 8) {
                // Gunakan kombinasi informasi device
                deviceId = Build.BOARD + Build.BRAND + Build.DEVICE + Build.HARDWARE + Build.MANUFACTURER + Build.MODEL + Build.PRODUCT;
                
                // Tambahkan UUID untuk memastikan keunikan
                deviceId += UUID.randomUUID().toString();
            } else {
                deviceId = androidId;
            }
            
            // Hash device ID untuk keamanan
            deviceId = hashDeviceId(deviceId);
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting device ID: " + e.getMessage());
            // Fallback ke UUID jika terjadi error
            deviceId = UUID.randomUUID().toString();
        }
        
        return deviceId;
    }
    
    /**
     * Hash device ID menggunakan SHA-256
     * @param deviceId Device ID yang akan di-hash
     * @return Hashed device ID
     */
    private static String hashDeviceId(String deviceId) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(deviceId.getBytes());
            
            // Konversi byte array ke string hex
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "Error hashing device ID: " + e.getMessage());
            return deviceId; // Return original device ID jika hashing gagal
        }
    }
}
