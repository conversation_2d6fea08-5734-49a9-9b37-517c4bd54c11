package com.webblogtoolbars.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model untuk request tracking lokasi
 */
public class LocationTrackingRequest {
    
    @SerializedName("api_key")
    private String apiKey;
    
    @SerializedName("nik")
    private String nik;
    
    @SerializedName("longitude")
    private String longitude;
    
    @SerializedName("latitude")
    private String latitude;
    
    public LocationTrackingRequest(String nik, String longitude, String latitude) {
        this.apiKey = "absensiku_api_key_2023";
        this.nik = nik;
        this.longitude = longitude;
        this.latitude = latitude;
    }
    
    public String getApiKey() {
        return apiKey;
    }
    
    public String getNik() {
        return nik;
    }
    
    public String getLongitude() {
        return longitude;
    }
    
    public String getLatitude() {
        return latitude;
    }
}
