<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".OfflineAbsensiActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Absensi Offline"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etNik"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="NIK"
                android:inputType="number" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etNama"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Nama Lengkap"
                android:inputType="textPersonName" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Jenis Absensi:"
            android:textSize="16sp"
            android:layout_marginBottom="8dp" />

        <RadioGroup
            android:id="@+id/rgJenisAbsen"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <RadioButton
                android:id="@+id/rbMasuk"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Masuk"
                android:checked="true" />

            <RadioButton
                android:id="@+id/rbKeluar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Keluar" />
        </RadioGroup>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <ImageView
                android:id="@+id/ivFoto"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:scaleType="centerCrop"
                android:background="#EEEEEE"
                android:src="@drawable/ic_camera" />

            <Button
                android:id="@+id/btnAmbilFoto"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ambil Foto"
                android:layout_gravity="center" />
        </FrameLayout>

        <Button
            android:id="@+id/btnSubmit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Submit Absensi"
            android:layout_marginTop="16dp" />

        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Data akan disimpan secara lokal dan akan dikirim ke server saat koneksi tersedia."
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginTop="16dp" />

        <TextView
            android:id="@+id/tvPendingData"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Data tertunda: 0"
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginTop="8dp" />

    </LinearLayout>
</ScrollView>
