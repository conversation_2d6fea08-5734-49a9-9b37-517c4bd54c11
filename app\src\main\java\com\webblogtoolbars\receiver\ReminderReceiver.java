package com.webblogtoolbars.receiver;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.webblogtoolbars.MainActivity;
import com.webblogtoolbars.R;
import com.webblogtoolbars.reminder.AttendanceReminderManager;

import java.util.Calendar;

public class ReminderReceiver extends BroadcastReceiver {
    private static final String TAG = "ReminderReceiver";
    private static final int CHECKIN_NOTIFICATION_ID = 1001;
    private static final int CHECKOUT_NOTIFICATION_ID = 1002;

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();

        // Handle boot completed
        if (Intent.ACTION_BOOT_COMPLETED.equals(action)) {
            Log.d(TAG, "Boot completed, rescheduling reminders");
            AttendanceReminderManager reminderManager = new AttendanceReminderManager(context);
            reminderManager.scheduleAllReminders();
            return;
        }

        // Periksa hari saat ini (jangan tampilkan notifikasi di hari Sabtu dan Minggu)
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

        if (dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY) {
            Log.d(TAG, "Skipping notification - weekend");
            return;
        }

        // Dapatkan data dari intent
        String type = intent.getStringExtra("type");
        String message = intent.getStringExtra("message");
        String channelId = intent.getStringExtra("channelId");

        if (type == null || message == null || channelId == null) {
            Log.e(TAG, "Missing required extras in intent");
            return;
        }

        Log.d(TAG, "Received reminder: type=" + type + ", message=" + message);

        // Tampilkan notifikasi
        showNotification(context, type, message, channelId);

        // Putar suara
        playSound(context, type);
    }

    private void showNotification(Context context, String type, String message, String channelId) {
        // Intent untuk membuka MainActivity saat notifikasi diklik
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);

        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // Intent untuk menghapus notifikasi
        Intent deleteIntent = new Intent(context, MainActivity.class);
        deleteIntent.setAction("DISMISS_NOTIFICATION");

        PendingIntent deletePendingIntent = PendingIntent.getActivity(
            context,
            1,
            deleteIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // Dapatkan URI sound berdasarkan tipe
        Uri soundUri;
        if ("checkin".equals(type)) {
            soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/raw/absen_masuk");
        } else {
            soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/raw/absen_pulang");
        }

        // Buat notifikasi
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentTitle("Pengingat Absensi")
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setAutoCancel(true)
            .setOngoing(false)
            .setSound(soundUri)
            .setContentIntent(pendingIntent)
            .setDeleteIntent(deletePendingIntent)
            .addAction(android.R.drawable.ic_menu_close_clear_cancel, "Hapus", deletePendingIntent)
            .setDefaults(NotificationCompat.DEFAULT_VIBRATE | NotificationCompat.DEFAULT_LIGHTS)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);

        // Tampilkan notifikasi
        NotificationManager notificationManager =
            (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        // Gunakan ID notifikasi yang berbeda untuk setiap tipe
        int notificationId = "checkin".equals(type) ? CHECKIN_NOTIFICATION_ID : CHECKOUT_NOTIFICATION_ID;

        try {
            notificationManager.notify(notificationId, builder.build());
            Log.d(TAG, "Notification shown: id=" + notificationId);
        } catch (Exception e) {
            Log.e(TAG, "Error showing notification: " + e.getMessage());
        }
    }

    private void playSound(Context context, String type) {
        try {
            // Dapatkan URI sound berdasarkan tipe
            Uri soundUri;
            if ("checkin".equals(type)) {
                soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/raw/absen_masuk");
            } else {
                soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/raw/absen_pulang");
            }

            // Putar suara menggunakan MediaPlayer
            MediaPlayer mediaPlayer = MediaPlayer.create(context, soundUri);
            if (mediaPlayer != null) {
                // Set volume ke maksimum
                AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
                int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, maxVolume, 0);

                mediaPlayer.setOnCompletionListener(mp -> {
                    mp.release();
                    Log.d(TAG, "Sound playback completed");
                });

                mediaPlayer.start();
                Log.d(TAG, "Sound playback started");
            } else {
                Log.e(TAG, "Failed to create MediaPlayer");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error playing sound: " + e.getMessage());
        }
    }
}
