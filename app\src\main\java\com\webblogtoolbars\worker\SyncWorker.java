package com.webblogtoolbars.worker;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.webblogtoolbars.api.AbsensiApiService;
import com.webblogtoolbars.api.ApiClient;
import com.webblogtoolbars.database.AbsensiDatabase;
import com.webblogtoolbars.model.AbsensiData;
import com.webblogtoolbars.model.AbsensiRequest;
import com.webblogtoolbars.model.ApiResponse;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Response;

/**
 * Worker untuk sinkronisasi data absensi ke server
 */
public class SyncWorker extends Worker {

    private static final String TAG = "SyncWorker";

    public SyncWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }

    @NonNull
    @Override
    public Result doWork() {
        Log.d(TAG, "Mulai sinkronisasi data absensi");

        AbsensiDatabase database = AbsensiDatabase.getDatabase(getApplicationContext());

        // Gunakan transaksi database untuk memastikan konsistensi data
        try {
            // Ambil data yang belum disinkronkan
            List<AbsensiData> unsyncedData = database.absensiDao().getUnsyncedAbsensi();

            if (unsyncedData.isEmpty()) {
                Log.d(TAG, "Tidak ada data yang perlu disinkronkan");
                return Result.success();
            }

            Log.d(TAG, "Jumlah data yang akan disinkronkan: " + unsyncedData.size());

            AbsensiApiService apiService = ApiClient.getClient().create(AbsensiApiService.class);
            boolean allSuccess = true;

            // Simpan ID data yang berhasil disinkronkan
            List<Integer> successfulIds = new ArrayList<>();

            // Proses satu per satu data yang belum disinkronkan
            for (AbsensiData absensiData : unsyncedData) {
                // Buat request API
                AbsensiRequest request = AbsensiRequest.fromAbsensiData(absensiData);

                try {
                    Log.d(TAG, "Mengirim data: " + absensiData.getId() + ", NIK: " + absensiData.getNik() + ", Tanggal: " + absensiData.getTanggal());

                    // Kirim data ke server
                    Call<ApiResponse> call = apiService.submitAbsensi(request);
                    Response<ApiResponse> response = call.execute();

                    if (response.isSuccessful() && response.body() != null) {
                        if (response.body().isStatus()) {
                            // Tambahkan ID ke daftar berhasil
                            successfulIds.add(absensiData.getId());

                            // Hapus data langsung setelah berhasil dikirim
                            try {
                                database.absensiDao().deleteById(absensiData.getId());
                                Log.d(TAG, "Data dengan ID " + absensiData.getId() + " langsung dihapus setelah berhasil dikirim");
                            } catch (Exception e) {
                                Log.e(TAG, "Error saat menghapus data langsung: " + e.getMessage());
                            }

                            Log.d(TAG, "Berhasil sinkronisasi data: " + absensiData.getId() + ", Pesan: " + response.body().getMessage());
                        } else {
                            allSuccess = false;
                            Log.e(TAG, "Gagal sinkronisasi data: " + absensiData.getId() + ", Pesan: " + response.body().getMessage());
                        }
                    } else {
                        allSuccess = false;
                        String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                        Log.e(TAG, "Gagal sinkronisasi data: " + absensiData.getId() + ", Kode: " + response.code() + ", Error: " + errorBody);
                    }
                } catch (Exception e) {
                    allSuccess = false;
                    Log.e(TAG, "Error saat sinkronisasi data " + absensiData.getId() + ": " + e.getMessage());
                    e.printStackTrace();
                }

                // Tambahkan delay kecil antara request untuk menghindari overload server
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // Hapus semua data untuk memastikan tidak ada data yang tersisa
            try {
                // Hapus semua data dari database
                database.absensiDao().deleteAllData();
                Log.d(TAG, "Semua data dihapus dari database lokal untuk memastikan tidak ada data yang tersisa");

                // Hapus data dari cache untuk memastikan data yang diambil berikutnya adalah data terbaru
                database.clearAllTables();

                // Periksa lagi apakah masih ada data yang belum disinkronkan
                List<AbsensiData> remainingData = database.absensiDao().getUnsyncedAbsensi();
                Log.d(TAG, "Setelah sinkronisasi dan penghapusan, tersisa " + remainingData.size() + " data");

                if (!remainingData.isEmpty()) {
                    Log.d(TAG, "Masih ada data tersisa, mencoba hapus sekali lagi");
                    database.absensiDao().deleteAllData();
                    database.clearAllTables();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error saat menghapus semua data: " + e.getMessage());
            }

            return allSuccess ? Result.success() : Result.retry();

        } catch (Exception e) {
            Log.e(TAG, "Error fatal saat sinkronisasi: " + e.getMessage());
            e.printStackTrace();
            return Result.failure();
        }
    }
}
