<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    tools:context=".AgreementActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:src="@mipmap/logo_absen_agreement" />

        <TextView
            android:id="@+id/tvAgreementTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="Persetujuan Penggunaan Aplikasi"
            android:textColor="#001AFF"
            android:textSize="20sp"
            android:textStyle="bold" />

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="#F5F5F5"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Dengan ini saya menyatakan telah membaca dan menyetujui ketentuan berikut:"
                    android:textColor="#000000"
                    android:textSize="16sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="1. Saya wajib melakukan absensi masuk dan keluar menggunakan aplikasi ini."
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="2. Metode absensi dilakukan dengan:"
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    android:text="• Foto Selfie sebagai bukti kehadiran."
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    android:text="• Barcode (jika tersedia di lokasi kerja)."
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    android:text="• GPS untuk memastikan lokasi absensi."
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    android:text="• Pelacakan lokasi saat jam kerja."
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="3.Saya dilarang melakukan manipulasi, seperti menggunakan foto lama, fake GPS, atau titip absen."
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="4. Data absensi saya akan digunakan hanya untuk keperluan internal perusahaan dan dijaga kerahasiaannya."
                    android:textColor="#000000"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:text="5.Pelanggaran terhadap ketentuan ini dapat dikenakan sanksi disiplin sesuai kebijakan perusahaan."
                    android:textColor="#000000"
                    android:textSize="14sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <Button
            android:id="@+id/btnAgree"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="#006eff"
            android:padding="12dp"
            android:text="Setuju &amp; Lanjut Login"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btnDisagree"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:padding="12dp"
            android:text="Tidak Setuju"
            android:textColor="#D000FF"
            android:textSize="16sp" />

    </LinearLayout>
</ScrollView>
